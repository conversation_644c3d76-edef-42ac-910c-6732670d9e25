{"name": "syense-console", "version": "4.2.1", "description": "A magical vue admin. An out-of-box UI solution for enterprise applications. Newest development stack of vue. Lots of awesome features", "author": "syense.cn", "license": "MIT", "scripts": {"dev": "vue-cli-service serve", "build:prod": "vue-cli-service build", "build:stage": "vue-cli-service build --mode staging", "preview": "node build/index.js --preview", "lint": "eslint --ext .js,.vue src", "test:unit": "jest --clearCache && vue-cli-service test:unit", "test:ci": "npm run lint && npm run test:unit", "svgo": "svgo -f src/icons/svg --config=src/icons/svgo.yml", "new": "plop"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{js,vue}": ["eslint --fix", "git add"]}, "keywords": ["vue", "admin", "dashboard", "element-ui", "boilerplate", "admin-template", "management-system"], "repository": {"type": "git", "url": "git+https://github.com/PanJiaChen/vue-element-admin.git"}, "bugs": {"url": "https://github.com/PanJiaChen/vue-element-admin/issues"}, "dependencies": {"@svgdotjs/svg.js": "^3.2.0", "@toast-ui/editor": "^3.1.3", "axios": "0.18.1", "clipboard": "2.0.4", "codemirror": "5.45.0", "driver.js": "0.9.5", "dropzone": "5.5.1", "echarts": "^5.2.0", "echarts-gl": "^2.0.8", "element-ui": "^2.15.5", "file-saver": "2.0.1", "fuse.js": "3.4.4", "js-cookie": "2.2.0", "jsonlint": "1.6.3", "jszip": "3.2.1", "mavon-editor": "^2.10.4", "normalize.css": "7.0.0", "nprogress": "0.2.0", "path-to-regexp": "2.4.0", "postcss-px2rem": "^0.3.0", "px2rem-loader": "^0.1.9", "screenfull": "4.2.0", "showdown": "1.9.0", "sortablejs": "1.8.4", "svgedit": "^7.3.3", "swiper": "^3.4.2", "vue": "2.6.10", "vue-animate-number": "^0.4.2", "vue-awesome-swiper": "^3.1.3", "vue-count-to": "1.0.13", "vue-drag-resize": "^1.5.4", "vue-grid-layout": "^2.3.12", "vue-router": "3.0.2", "vue-seamless-scroll": "^1.1.23", "vue-splitpane": "1.0.4", "vue-svgicon": "^3.2.6", "vue-swatches": "^2.1.1", "vue-timeago": "^5.1.3", "vue-wxlogin": "^1.0.4", "vuedraggable": "^2.20.0", "vuex": "3.1.0", "xlsx": "0.14.1", "xlsx-style": "^0.8.13"}, "devDependencies": {"@babel/core": "7.0.0", "@babel/register": "7.0.0", "@vue/cli-plugin-babel": "3.5.3", "@vue/cli-plugin-eslint": "^3.9.1", "@vue/cli-plugin-unit-jest": "3.5.3", "@vue/cli-service": "3.5.3", "@vue/test-utils": "1.0.0-beta.29", "autoprefixer": "^9.5.1", "babel-core": "7.0.0-bridge.0", "babel-eslint": "10.0.1", "babel-jest": "23.6.0", "chalk": "2.4.2", "chokidar": "2.1.5", "connect": "3.6.6", "eslint": "5.15.3", "eslint-plugin-vue": "5.2.2", "github-markdown-css": "^5.2.0", "highlight.js": "^11.8.0", "html-loader": "^0.5.4", "html-webpack-plugin": "3.2.0", "husky": "1.3.1", "lint-staged": "8.1.5", "mockjs": "1.0.1-beta3", "node-sass": "^4.9.0", "plop": "2.3.0", "raw-loader": "^4.0.2", "runjs": "^4.3.2", "sass-loader": "^7.1.0", "script-ext-html-webpack-plugin": "2.1.3", "script-loader": "0.7.2", "serve-static": "^1.13.2", "svg-sprite-loader": "4.1.3", "svgo": "1.2.0", "vue-codemirror": "^4.0.6", "vue-template-compiler": "^2.6.10", "vue-wxlogin": "^1.0.1"}, "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions"]}