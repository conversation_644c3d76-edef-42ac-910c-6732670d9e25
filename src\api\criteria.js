import request from '@/utils/request'

export function getEcCriteria() {
  return request({
    url: '/saianapi/v1/ec_criteria',
    method: 'get'
  })
}

export function createEcCriteria(data) {
  return request({
    url: '/saianapi/v1/ec_criteria',
    method: 'post',
    data
  })
}

export function getEcCriterion(id) {
  return request({
    url: '/saianapi/v1/ec_criteria/' + id,
    method: 'get'
  })
}

export function updateEcCriterion(id, data) {
  return request({
    url: '/saianapi/v1/ec_criteria/' + id,
    method: 'put',
    data
  })
}

export function deleteEcCriterion(id) {
  return request({
    url: '/saianapi/v1/ec_criteria/' + id,
    method: 'delete'
  })
}

export function partialUpdateEcCriterion(id) {
  return request({
    url: '/saianapi/v1/ec_criteria/' + id,
    method: 'patch'
  })
}
