import request from '@/utils/request'
import { getProject } from '@/utils/auth'

/**
 * 获取冷源信息，旧版要用
 */
export function getColdSource(name) {
  return request({
    url: '/saianapi/v1/cold_sources?prjid=' + getProject() + '&name=' + name,
    method: 'GET'
  })
}

/**
 * 获取冷源信息
 */
export function getColdSources(name, no_coords_arr = false) {
  const params = {
    prjid: getProject(),
    name
  }
  if (no_coords_arr) {
    params['no_coords_arr'] = 1
  }
  return request({
    url: '/saianapi/v1/cold_sources',
    method: 'GET',
    params
  })
}

/**
 * 获取冷源详情
 * @param {*} csId
 */
export function getCsDetail(csId) {
  return request({
    url: '/saianapi/v1/cold_sources/' + csId,
    method: 'GET'
  })
}

/**
 * 添加冷源信息
 */
export function saveColdSource(data) {
  return request({
    url: '/saianapi/v1/cold_sources',
    method: 'POST',
    data: {
      uni_name: data.uni_name,
      name: data.name,
      image: data.image
    }
  })
}

/**
 * 添加冷源信息
 * @param {*} cs, { name: '冷源名字，单独的名字，区别于冷源设备名字', image: '冷源平面图id'}
 */
export function addColdSource(cs) {
  return request({
    url: '/saianapi/v1/cold_sources',
    method: 'POST',
    data: cs
  })
}

/**
 * 修改冷源信息
 */
export function updateColdSource(cs) {
  const data = {}
  if (typeof cs.uni_name !== 'undefined' && cs.uni_name !== '') {
    data.uni_name = cs.uni_name
  }
  if (typeof cs.name !== 'undefined' && cs.name !== '') {
    data.name = cs.name
  }
  if (typeof cs.mac !== 'undefined' && cs.mac !== '') {
    data.mac = cs.mac
  }

  if (typeof cs.image !== 'undefined' && cs.image !== '') {
    data.image = cs.image
  }

  if (typeof cs.coords_arr !== 'undefined') {
    data.coords_arr = cs.coords_arr
  }

  if (typeof cs.devices !== 'undefined' && cs.devices.length !== 0) {
    data.devices = cs.devices
  }

  return request({
    url: '/saianapi/v1/cold_sources/' + cs.csId,
    method: 'PUT',
    data: data
  })
}

/**
 * 移除冷源信息
 * @param {*} csId
 */
export function removeColdSource(csId) {
  return request({
    url: '/saianapi/v1/cold_sources/' + csId,
    method: 'DELETE'
  })
}

export function updateCs(csid, params) {
  return request({
    url: '/saianapi/v1/cold_sources/' + csid,
    method: 'PUT',
    params
  })
}

/**
 * 获取冷源运行记录下载列表
 */
export function getRrsDlTasks(params) {
  return request({
    url: '/saianapi/v1/rrs_dl_tasks',
    method: 'get',
    params
  })
}

/**
 * 创建运行记录下载任务
 * @param params: {device_id, data_at}
 * @returns {AxiosPromise}
 */
export function createRrsDlTask(data) {
  return request({
    url: '/saianapi/v1/rrs_dl_tasks',
    method: 'post',
    data
  })
}

/**
 * 删除运行记录下载任务
 * @param taskId
 * @returns {AxiosPromise}
 */
export function deleteRrsDlTask(taskId) {
  return request({
    url: '/saianapi/v1/rrs_dl_tasks/' + taskId,
    method: 'delete'
  })
}

/**
 * 查询所有cop
 * @returns {*}
 */
export function getCops() {
  return request({
    url: '/saianapi/v5/cs_cops',
    method: 'get'
  })
}

/**
 * 查询节能报告导出记录
 * @returns {*}
 */
export function getDlTasks(params) {
  return request({
    url: '/saianapi/v5/dl_tasks',
    method: 'get',
    params
  })
}

/**
 * 删除节能报告导出记录
 * @param taskId
 * @returns {*}
 */
export function deleteDlTask(taskId) {
  return request({
    url: '/saianapi/v5/dl_tasks/' + taskId,
    method: 'delete'
  })
}

/**
 * 系统节能报告导出
 * @param data
 * @returns {*}
 */
export function createEsReport(data) {
  return request({
    url: '/saianapi/v5/sys_ec_report',
    method: 'post',
    data
  })
}
