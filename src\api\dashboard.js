import request from '@/utils/request'
import { getProject } from '@/utils/auth'

/**
 * 获取仪表板
 */
export function getDashboard() {
  return request({
    url: '/saianapi/v2/dashboards/web?project_id=' + getProject(),
    method: 'GET'
  })
}

export function getProjectCharts() {
  return request({
    url: '/saianapi/v1/project_charts',
    method: 'GET'
  })
}

export function getWebCharts() {
  return request({
    url: '/saianapi/v1/web_charts',
    method: 'GET'
  })
}

/**
 *
 * @param {web_chart_id, num} data
 * @returns
 */
export function configProjectChart(data) {
  return request({
    url: '/saianapi/v1/project_charts',
    method: 'POST',
    data
  })
}

/**
 *
 * @param {*} id
 * @param {web_chart_id} data
 * @returns
 */
export function updateProjectChart(id, data) {
  return request({
    url: '/saianapi/v1/project_charts/' + id,
    method: 'PUT',
    data
  })
}

export function getProjectChartData(id) {
  return request({
    url: '/saianapi/v1/project_charts/' + id,
    method: 'GET'
  })
}

export function deleteProjectChart(id) {
  return request({
    url: '/saianapi/v1/project_charts/' + id,
    method: 'DELETE'
  })
}

/**
 * 查询冷源能耗关联配置列表
 * @param {csid, ec_type} params
 */
export function getEerSettings(params) {
  return request({
    url: '/saianapi/v1/ec_sources',
    method: 'GET',
    params
  })
}

/**
 * 查询仪表列表， 电表 ： 10, 冷量表： 30
 * @param {*} ec_type
 * @returns
 */
export function getEcMeter(ec_type) {
  return request({
    url: '/saianapi/v1/ec_meters?ec_type=' + ec_type,
    method: 'GET'
  })
}

// 全量更新 ec_source
export function addEcSources(data) {
  return request({
    url: '/saianapi/v1/ec_sources',
    method: 'POST',
    data
  })
}

export function getCsEerAnalyses(params) {
  return request({
    url: '/saianapi/v1/cs_eer_analyses',
    params,
    method: 'GET'
  })
}

/**
 * 查询末端数据配置列表
 */
export function getAcTerminals() {
  return request({
    url: '/saianapi/v1/ac_terminals',
    method: 'GET'
  })
}

/**
 *
 * @param {dpid: 设备类型id， cooling_cap: 制冷量} data
 * @returns
 */
export function addAcTerminals(data) {
  return request({
    url: '/saianapi/v1/ac_terminals',
    method: 'POST',
    data
  })
}

/**
 *
 * @param {分组id} id
 * @param {cooling_cap} data 制冷量
 * @returns null
 */
export function updateAcTerminals(id, data) {
  return request({
    url: '/saianapi/v1/ac_terminals/' + id,
    method: 'PUT',
    data
  })
}

export function deleteAcTerminals(id) {
  return request({
    url: '/saianapi/v1/ac_terminals/' + id,
    method: 'DELETE'
  })
}

/**
 * 查询末端供冷需求冷量统计数据
 * @param {form, to} params
 * @returns
 */
export function getActColdStats(params) {
  return request({
    url: '/saianapi/v1/act_cold_stats',
    method: 'GET',
    params
  })
}

/**
 * 查询气温
 * @param {form, to,type} params
 * @returns
 */
export function getTemp(params) {
  return request({
    url: '/saianapi/v1/project_weather',
    method: 'GET',
    params
  })
}

/**
  *新的总览面板接口,4个
*/
export function getDashboardNew() {
  return request({
    url: '/saianapi/v2/dashboards/web?project_id=' + getProject(),
    method: 'GET'
  })
}
/**
 *获取所有面板设置项
*/
export function getProjectSettingsAll() {
  return request({
    url: '/saianapi/v5/project_settings',
    method: 'GET'
  })
}

/**
 *获取总览面板设置项
*/
export function getDashboardLayout() {
  return request({
    url: '/saianapi/v5/project_settings/dashboard_layout',
    method: 'GET'
  })
}
/**
 *更改总览面板设置项
 *@param data
*/
export function updateDashboardLayout(data) {
  return request({
    url: '/saianapi/v5/project_settings',
    method: 'PUT',
    data: { 'dashboard_layout': data }
  })
}
/**
 *获取总览面板设置项
*/
export function getSettingsDashboardLayout() {
  return request({
    url: '/saianapi/v5/project_settings',
    method: 'GET'
  })
}

/**
 *总览面板末端概况图表
 *@param data
*/
export function getTerminalProfile(params) {
  return request({
    url: 'saianapi/v5/terminals_stats',
    method: 'get',
    params
  })
}

/**
 *总览面板能耗维度属性
 *@param params
*/
export function getDimensionEcItems(params) {
  return request({
    url: 'saianapi/v5/dimension_ec_items',
    method: 'get',
    params
  })
}
/**
 *总览面板维度能耗图表
 *@param params
*/
export function getDimensionSummaries(params) {
  return request({
    url: 'saianapi/v5/ec_summaries',
    method: 'get',
    params
  })
}

/**
 *总览冷源概况图表
 *@param params
*/
export function getColdSourceOverview(params) {
  return request({
    url: 'saianapi/v5/cs_overview',
    method: 'get',
    params
  })
}

/**
 *能耗分析与预测
 *@param params
*/
export function getEcAnalysisPredictions(params) {
  return request({
    url: 'saianapi/v5/ec_analysis_predictions',
    method: 'get',
    params
  })
}

/**
 *用户活跃度排名
 *@param params
*/
export function getActiveUsers(params) {
  return request({
    url: 'saianapi/v5/active_users',
    method: 'get',
    params
  })
}
