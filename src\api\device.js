import request from '@/utils/request'
import request_drf from '@/utils/request-drf'
import request_prj from '@/utils/request-prj'
import { getProject } from '@/utils/auth'

/**
 * 查询设备列表
 * query = { page, per_page, project_id }
 * page为页数，per_page为每页多少个，project_id为项目id
 * @param {*} query
 */
export function getList(query) {
  const params = Object.assign({}, query, { project_id: getProject() })
  return request({
    url: '/saianapi/v1/web_devices',
    method: 'get',
    params
  })
}

/**
 * 九宫格页面查询设备列表
 *
 */
export function getDeviceList(params) {
  return request({
    url: '/saianapi/v1/web_devices',
    method: 'get',
    params
  })
}

/**
 * 获取简单的设备列表
 * @param params
 * @returns {*}
 */
export function getDeviceList2(params) {
  return request({
    url: '/saianapi/v2/web_devices',
    method: 'get',
    params
  })
}

/**
 * 查询所有的设备
 *
 */
export function getAllDevice(params) {
  params.per_page = 2000
  return request({
    url: '/saianapi/v1/web_devices',
    method: 'get',
    params
  })
}

/**
 * 查询设备详情
 * @param {*} id
 */
export function getDevice(id) {
  return request({
    url: '/saianapi/v1/web_devices/' + id,
    // url: '/saianapi/v2/devices/' + id,
    method: 'get'
  })
}

export function getDeviceInfo(id) {
  return request({
    url: '/saianapi/v2/devices/' + id,
    method: 'get'
  })
}

export function getLivingDetections(id) {
  return request({
    url: '/saianapi/v1/living_detections/' + id,
    method: 'get'
  })
}

/**
 * 修改设备昵称
 * @param {*} id
 * @param {*} nick_name
 */
export function updateNickName(id, nick_name) {
  return request({
    url: '/saianapi/v1/web_devices/' + id,
    method: 'put',
    data: { nick_name }
  })
}

/**
 * 获取房间的的设备列表
 *
 */
export function getUnOrBindDevices() {
  return request({
    url: '/saianapi/v1/web_unbind_devices',
    method: 'get',
    params: {
      project_id: getProject()
    }
  })
}

/**
 * 根据设备id获取配置分类
 */
export function getDeviceType(device_id) {
  return request({
    url: '/saianapi/v1/devices/' + device_id + '/attribute_types',
    method: 'get'
  })
}

/**
 * 根据配置分类获取设备配置项
 */
export function getDeviceAttributesByType(type_id, device_id) {
  return request({
    url:
      '/saianapi/v1/attribute_types/' +
      type_id +
      // '/web_device_attributes?device_id=' +
      '/device_attributes?device_id=' +
      device_id,
    method: 'get'
  })
}

/**
 * 获取属性分类
 * @param params
 */
export function getDeviceAttributeTypes(params) {
  return request({
    url: 'saianapi/v5/attribute_types',
    method: 'get',
    params
  })
}

/**
 * 提交设备配置变更
 * data = { device_id, changes }
 * device_id为设备id，changes为变更的参数，格式为json字符串，比如：{\"SysReportTime\": 301}
 */
export function sendChanges(data) {
  return request({
    url: '/saianapi/v1/web_device_changes',
    method: 'post',
    data
  })
}

/**
 * 设备离在线统计
 * query: { project_id }
 */
export function device_live_stats() {
  return request({
    url: '/saianapi/v1/device_live_stats',
    method: 'get',
    params: {
      project_id: getProject()
    }
  })
}

/*
 * 管理设备报表
 *
 */
export function getReportsName(params) {
  return request({
    url: '/saianapi/v1/reports',
    method: 'get',
    params
  })
}

function getRequestByProject() {
  const drfStatsProjects = ['11', '27', '46']
  return drfStatsProjects.indexOf(getProject()) >= 0 ? request_drf : request
}

/**
 * Web设备图表报表列表
 * @param params
 */
export function getWebReports(params) {
  return getRequestByProject()({
    url: '/saianapi/v1/web_reports',
    method: 'get',
    params
  })
}

/*
 * 获取图表数据
 *
 */
export function getDeviceStats(params) {
  return getRequestByProject()({
    url: '/saianapi/v2/device_stats',
    method: 'get',
    params
  })
}

export function generateDeviceStatsDownloadUrl(data) {
  return request({
    url: '/saianapi/v5/device_stats',
    method: 'post',
    data
  })
}

/**
 * 查询所有分类
 */
export function getDeviceSort() {
  return request({
    url: '/saianapi/v1/device_types',
    method: 'get'
  })
}

/**
 *  查询所有设备的设备类型
 */
export function getDeviceProtoes() {
  return request({
    url: '/saianapi/v1/device_prototypes?prjid=' + getProject(),
    method: 'get'
  })
}

/**
 *  查询所有终端的设备类型
 */
export function getTerminalProtoes() {
  return request({
    url: '/saianapi/v5/device_prototypes',
    method: 'get'
  })
}

/**
 * 根据设备类型ID查询设备列表
 */
export function getDevicesByProtoId(protoId) {
  return request({
    url:
      '/saianapi/v1/web_devices?prjid=' + getProject() + '&protoid=' + protoId,
    method: 'get'
  })
}

export function getDevicesIssue(params) {
  return request({
    // url: '/saianapi/v1/web_devices?prjid=' + getProject() + '&protoid=' + protoId,
    url: `/saianapi/v1/projects/${getProject()}/device_issues`,
    method: 'get',
    params
  })
}

export function changeDeviceConfig(data) {
  return request({
    url: `/saianapi/v1/device_changes`,
    method: 'post',
    data
  })
}

/**
 * 根据设备类型id获取该类型详情
 * @param {Number} type
 */
export function getDeviceTypeDetail(params) {
  return request({
    url: `/saianapi/v1/projects/${getProject()}/devices`,
    method: 'get',
    params
  })
}

export function updateChildDeviceNickname(deviceId, data) {
  return request({
    url: '/saianapi/v1/devices/' + deviceId,
    method: 'put',
    data
  })
}

export function getDeviceLog(params) {
  return request({
    url: '/saianapi/v1/device_logs',
    method: 'get',
    params
  })
}

/**
 * 获取时间控制列表
 * @param did 设备ID
 */
export function getDeviceTimers(did) {
  return request({
    url: '/saianapi/v1/device_timers?device_id=' + did,
    method: 'get'
  })
}

/**
 * 更新时间控制参数
 * @param timerId
 * @param data
 */
export function updateDeviceTimer(timerId, data) {
  return request({
    url: '/saianapi/v1/device_timers/' + timerId,
    method: 'put',
    data
  })
}

/**
 * 添加时间控制
 * @param data
 */
export function createDeviceTimer(data) {
  return request({
    url: '/saianapi/v1/device_timers',
    method: 'post',
    data
  })
}

/**
 * 删除时间控制
 * @param timerId
 */
export function deleteDeviceTimer(timerId) {
  return request({
    url: '/saianapi/v1/device_timers/' + timerId,
    method: 'delete'
  })
}

/**
 * 修改设备备注
 * @param {*} id
 * @param {*} remark
 */
export function updateDeviceRemark(id, remark) {
  return request({
    url: '/saianapi/v1/web_devices/' + id,
    method: 'put',
    data: { remark }
  })
}

/**
 * 查询附加设备信息
 */
export function getDeviceFcubd(deviceId) {
  return request({
    url: '/saianapi/v5/fcubd',
    method: 'get',
    params: { device_id: deviceId }
  })
}

/**
 * 查询子设备详情
 * @param id 设备id
 * @param prefix 子设备前缀
 * @param idx 子设备索引
 * @returns {AxiosPromise}
 */
export function getSubDeviceDetails(id, prefix, idx) {
  return request({
    url: '/saianapi/v3/devices/' + id,
    method: 'get',
    params: { prefix, idx }
  })
}

/**
 * 导出维度属性统计
 * @param data
 * @returns {AxiosPromise}
 */
export function generateDimensionStatsDownloadUrl(data) {
  return request({
    url: '/saianapi/v5/dimension_stats',
    method: 'post',
    data
  })
}

/*
 * 获取设备属性参数
 *
 */
export function getDeviceAttrs(params) {
  return request({
    url: '/saianapi/v5/device_attributes',
    method: 'get',
    params
  })
}

/*
 * 获取图表数据v2
 *
 */
export function getDeviceChart(params, id) {
  return request_prj({
    url: '/saianapi/v5/device_attributes/' + id,
    method: 'get',
    params
  })
}
/*
 * 导出图表数据v2
 *
 */
export function exportDeviceChart(data) {
  return request_prj({
    url: '/saianapi/v5/device_attributes',
    method: 'post',
    data
  })
}

/**
 * 查询设备绑定的 sim 卡信息
 * @param mac
 * @returns {*}
 */
export function getSimInfo(mac) {
  return request({
    url: 'saianapi/v5/sy_sims/' + mac,
    method: 'get'
  })
}

/**
 * 设备取消重试
 * @param id
 * @returns {*}
 */
export function deviceRetryCancel(id, data) {
  return request({
    url: 'saianapi/v1/device_ctrl_logs/' + id,
    method: 'put',
    data
  })
}

/**
 * 查询所有LTA仪表
 */
export function getLTAMeters() {
  return request({
    url: 'saianapi/v5/lta_meters',
    method: 'get'
  })
}

/**
 * 人工校正仪表读数
 * @param data
 */
export function correctLTAMeter(data) {
  return request({
    url: 'saianapi/v5/lta_meters',
    method: 'put',
    data
  })
}
