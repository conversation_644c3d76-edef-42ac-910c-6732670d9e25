import request from '@/utils/request'

/**
  维度类型列表
 *@param params
*/
export function getDimensionTypes(params) {
  return request({
    url: 'saianapi/v5/dimension_types',
    method: 'get',
    params
  })
}
/**
  维度列表
 *@param params
*/
export function getDimensions(params) {
  return request({
    url: 'saianapi/v5/dimensions',
    method: 'get',
    params
  })
}

/**
 * 创建维度
 */
export function createDimension(data) {
  return request({
    url: 'saianapi/v5/dimensions',
    method: 'post',
    data
  })
}

/**
 * 更新维度信息
 */
export function updateDimension(id, data) {
  return request({
    url: 'saianapi/v5/dimensions/' + id,
    method: 'put',
    data
  })
}

/**
 * 查询维度用户
 */
export function getDimensionUsers(params) {
  return request({
    url: 'saianapi/v5/dimension_users',
    method: 'get',
    params
  })
}

/**
 * 绑定用户到维度
 */
export function bindDimensionUsers(data) {
  return request({
    url: 'saianapi/v5/dimension_users',
    method: 'post',
    data
  })
}

/**
 * 查询单位耗电排名
 */
export function getUnitRankings(params) {
  return request({
    url: 'saianapi/v5/unit_rankings',
    method: 'get',
    params
  })
}

/**
 * 查询单位用电量
 * @param params
 */
export function getUnitCons(params) {
  return request({
    url: 'saianapi/v5/unit_cons',
    method: 'get',
    params
  })
}

/**
 * 查询单位房间用电量排名
 * @param params
 */
export function getUnitRoomConsRankings(params) {
  return request({
    url: 'saianapi/v5/unit_room_rankings',
    method: 'get',
    params
  })
}

/**
  维度属性
 *@param params
*/
export function getDimensionAttributes(params) {
  return request({
    url: 'saianapi/v5/dimension_attributes',
    method: 'get',
    params
  })
}

/**
 * 查询用户所属维度
 */
export function getUserDimensions(params) {
  return request({
    url: 'saianapi/v5/user_dimensions',
    method: 'get',
    params
  })
}

/**
 * 更新用户的所属维度
 * @param data
 */
export function bindUserDimensions(data) {
  return request({
    url: 'saianapi/v5/user_dimensions',
    method: 'post',
    data
  })
}

/**
 * 查询维度绑定的终端
 * @param params
 */
export function getDimensionTerminals(params) {
  return request({
    url: 'saianapi/v5/dimension_terminals',
    method: 'get',
    params
  })
}

/**
 * 绑定终端到维度
 * @param data
 */
export function bingDimensionTerminals(data) {
  return request({
    url: 'saianapi/v5/dimension_terminals',
    method: 'post',
    data
  })
}

/**
 * 查询维度属性，树状数据
 * @param params
 */
export function getDimensionAttrTree(params) {
  return request({
    url: 'saianapi/v5/dimension_attr_tree',
    method: 'get',
    params
  })
}
