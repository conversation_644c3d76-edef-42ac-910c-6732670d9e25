import request from '@/utils/request'
import { getProject } from '@/utils/auth'

/**
 * 查询项目详情
 * @param { * } data
 */
export function getEcProjectDetail() {
  return request({
    url: '/ecapi/v1/projects/' + getProject(),
    // url: '/saianapi/v5/projects/' + getProject(),
    method: 'get'
  })
}

/**
 * 建筑列表
 * @param {Object} params
 * @prop {Integer} params.page
 * @prop {Integer} params.per_page
 */
export function getEcBuildings(params) {
  return request({
    url: `/ecapi/v1/ec_buildings`,
    method: 'get',
    params
  })
}

/**
 * 查询分项列表
 * @param params
 * @returns {*}
 */

export function getEcItems(params) {
  return request({
    url: '/ecapi/v1/ec_items',
    method: 'get',
    params
  })
}

/**
 * 查询能耗汇总V2
 * @param params
 * @returns {*}
 */
export function getEcSummariesV2(params) {
  return request({
    url: '/ecapi/v2/ec_summaries',
    method: 'get',
    params
  })
}
/**
 * 查询能耗花费
 * @param params
 * @returns {*}
 */
export function getEcTotalStats(params) {
  return request({
    url: '/saianapi/v5/total_stats',
    method: 'get',
    params
  })
}

/**
 * 查询基准年总用电量
 * @param params
 * @returns {*}
 */
export function getEcCriteria(params) {
  return request({
    url: '/saianapi/v1/ec_criteria',
    method: 'get',
    params
  })
}

/**
 * 汇总环比分析报表
 * @param ec_type
 * @returns {*}
 */
export function getQoqStats(ec_type) {
  const params = { ec_type }
  return request({
    // url: '/ecapi/v1/qoq_stats',
    url: '/saianapi/v5/qoq_stats',
    method: 'get',
    params
  })
}

/**
 * 分类环比分析报表
 * @param params
 * @returns {*}
 */
export function getEtQoqStats(params) {
  return request({
    // url: '/ecapi/v1/et_qoq_stats',
    url: '/saianapi/v5/et_qoq_stats',
    method: 'get',
    params
  })
}
/**
 * 能耗报表
 * @param params
 * @returns {*}
 */
export function getEtReports(params) {
  return request({
    // url: '/ecapi/v1/et_qoq_stats',
    url: '/saianapi/v5/ec_reports',
    method: 'get',
    params
  })
}

/**
 * 能耗汇总导出
 * @param params
 * @returns {AxiosPromise}
 */
export function exportEcReport(params) {
  return request({
    url: '/saianapi/v5/ec_reports',
    method: 'post',
    params
  })
}

// 查询项目中有的能耗类型
export function getEcTypes() {
  return request({
    url: '/saianapi/v5/dimension_ec_types',
    method: 'get'
  })
}

/**
 * 查询能耗排名
 * @param params
 * @returns {*}
 */
export function getEcRanking(params) {
  return request({
    url: '/saianapi/v5/ec_rankings',
    method: 'get',
    params
  })
}

/**
 * 查询能耗对比
 * @param params
 * @returns {*}
 */
export function getEcComparison(params) {
  return request({
    url: '/saianapi/v5/ec_comparisons',
    method: 'get',
    params
  })
}

export function getEcConsumeCalc(params) {
  return request({
    url: '/saianapi/v5/ec_consume_calculation',
    method: 'get',
    params
  })
}
