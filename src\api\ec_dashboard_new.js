import request from '@/utils/request'
// import { getProject } from '@/utils/auth'

// 能耗总量
// unit_name单位名称
export function getEcSums(params) {
  return request({
    url: `/saianapi/v5/ec_sums`,
    method: 'get',
    params
  })
}

// 定额指标
// unit_name单位名称
export function getEcQuotas(params) {
  return request({
    url: `/saianapi/v5/ec_quotas`,
    method: 'get',
    params
  })
}

// 能耗趋势
// unit_name单位名称
// names		统计参数名，多个以逗号隔开，默认：标准煤,碳排放
export function getEcTrends(params) {
  return request({
    url: `/saianapi/v5/ec_trends`,
    method: 'get',
    params
  })
}

// 能耗累计
// unit_name单位名称
export function getEcTotals(params) {
  return request({
    url: `/saianapi/v5/ec_totals`,
    method: 'get',
    params
  })
}

// 设备用电排名
export function getEcRankings(params) {
  return request({
    // url: `/saianapi/v5/ec_rankings`,
    url: '/saianapi/v5/et_qoq_stats',
    method: 'get',
    params
  })
}

// 能耗（定额）排名
// unit_name单位名称
// name 定额名称，如：人均综合能耗
// 单位排名
export function getUnitRankings(params) {
  return request({
    url: `/saianapi/v5/unit_rankings`,
    method: 'get',
    params
  })
}

export function getUnitCons(params) {
  return request({
    url: `/saianapi/v5/unit_cons`,
    method: 'get',
    params
  })
}

// 单位设备用电排名（占比）
// unit_name单位名称
export function getUnitEcRankings(params) {
  return request({
    url: `/saianapi/v5/unit_ec_rankings`,
    method: 'get',
    params
  })
}

// 检测预警（即故障报警接口）
export function getWebIssues(params) {
  return request({
    url: `/saianapi/v1/web_issues`,
    method: 'get',
    params
  })
}

// 设备信息（现有接口上修改）
export function getCategoryTerminals(params) {
  return request({
    url: `/saianapi/v5/category_terminals`,
    method: 'get',
    params
  })
}
