/*
 * @Author: czf <EMAIL>
 * @Date: 2022-12-20 17:53:47
 * @LastEditors: czf <EMAIL>
 * @LastEditTime: 2024-04-12 15:43:23
 * @FilePath: \ac-web-standard\src\api\terminal.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

import request from '@/utils/request'

export function getTerminals(params) {
  Object.keys(params).forEach(key => {
    if (params[key] === '' || params[key] == null) {
      delete params[key]
    }
  })
  return request({
    url: '/saianapi/v5/terminals',
    method: 'get',
    params
  })
}

// 获取终端详情
export function getTerminalsDetail(id, params) {
  return request({
    url: '/saianapi/v5/terminals/' + id,
    method: 'get',
    params
  })
}

export function getTerminalsByCategory(params) {
  return request({
    url: '/saianapi/v5/category_terminals',
    method: 'get',
    params
  })
}

export function bindTerminalToCategory(data) {
  return request({
    url: '/saianapi/v5/category_terminals',
    method: 'post',
    data
  })
}

export function getCategories(params) {
  return request({
    url: '/saianapi/v5/categories',
    method: 'get',
    params
    // params: {
    //   acc_end: 20
    // }
  })
}

export function createCategory(data) {
  return request({
    url: '/saianapi/v5/categories',
    method: 'post',
    data: data
  })
}

export function changeCategoriesOrder(data) {
  return request({
    url: 'saianapi/v5/categories',
    method: 'put',
    data
  })
}

export function updateCategory(id, params) {
  return request({
    url: '/saianapi/v5/categories/' + id,
    method: 'put',
    data: params
  })
}

export function deleteCategory(id) {
  return request({
    url: '/saianapi/v5/categories/' + id,
    method: 'delete'
  })
}

export function updateTerminal(id, data) {
  return request({
    url: '/saianapi/v5/terminals/' + id,
    method: 'put',
    data
  })
}

// 终端搜索V2
export function getTerminalsSearch(params) {
  return request({
    url: '/saianapi/v5/terminal_searches',
    method: 'get',
    params
  })
}

// 客房空调

export function getRoomCondition() {
  return request({
    url: '/saianapi/v5/room_conditions',
    method: 'get'
  })
}

// 楼层列表

export function getFloors(params) {
  return request({
    url: '/saianapi/v5/floors',
    method: 'get',
    params
  })
}

// 楼层终端
export function getTerminalsByFloors(params) {
  return request({
    url: '/saianapi/v5/floor_terminals',
    method: 'get',
    params
  })
}

// 终端类型
export function getTerminalsAttributes(params) {
  return request({
    url: '/saianapi/v5/terminal_attributes',
    method: 'get',
    params
  })
}

// 查询终端类型列表
export function getTerminalsCategories(params) {
  return request({
    url: '/saianapi/v5/categories',
    method: 'get',
    params
  })
}

// 查询项目终端有哪些终端分类字符串
export function getTerminalLabels() {
  return request({
    url: 'saianapi/v5/terminal_labels',
    method: 'get'
  })
}
