<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="28" height="31" viewBox="0 0 28 31"><defs><style>.a{fill:url(#a);}.b{fill:url(#d);}.c{filter:url(#e);}.d{filter:url(#b);}</style><linearGradient id="a" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox"><stop offset="0" stop-color="#417be8"/><stop offset="1" stop-color="#0d83f8" stop-opacity="0.012"/></linearGradient><filter id="b" x="2" y="0" width="24" height="28" filterUnits="userSpaceOnUse"><feOffset dy="3" input="SourceAlpha"/><feGaussianBlur stdDeviation="3" result="c"/><feFlood flood-color="#0de0fc" flood-opacity="0.757"/><feComposite operator="in" in2="c"/><feComposite in="SourceGraphic"/></filter><linearGradient id="d" y2="1.187" xlink:href="#a"/><filter id="e" x="0" y="9" width="28" height="22" filterUnits="userSpaceOnUse"><feOffset input="SourceAlpha"/><feGaussianBlur stdDeviation="2" result="f"/><feFlood flood-color="#69c9f4" flood-opacity="0.796"/><feComposite operator="in" in2="f"/><feComposite in="SourceGraphic"/></filter></defs><g transform="translate(6 6)"><g class="d" transform="matrix(1, 0, 0, 1, -6, -6)"><rect class="a" width="6" height="10" transform="translate(11 6)"/></g><g class="c" transform="matrix(1, 0, 0, 1, -6, -6)"><path class="b" d="M8,0l8,10H0Z" transform="translate(22 25) rotate(180)"/></g></g></svg>