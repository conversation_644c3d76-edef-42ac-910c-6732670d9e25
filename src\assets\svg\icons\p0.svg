<?xml version="1.0" encoding="utf-8"?>
<svg viewBox="4.2054 7.9334 20.265 20.265" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="b" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#2a69f5"/>
      <stop offset="1" stop-color="#5b99ed"/>
    </linearGradient>
    <filter id="filter-1">
      <feOffset input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="1" result="d"/>
      <feFlood flood-color="#36d0eb" flood-opacity="0.6" result="e"/>
      <feComposite operator="out" in="SourceGraphic" in2="d"/>
      <feComposite operator="in" in="e"/>
      <feComposite operator="in" in2="SourceGraphic"/>
    </filter>
    <filter id="filter-2" x="0.117" y="1.84" width="26.4" height="29.16" filterUnits="userSpaceOnUse">
      <feOffset dy="3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="3" result="g"/>
      <feFlood flood-opacity="0.302"/>
      <feComposite operator="in" in2="g"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <style>.a{stroke:#fff;stroke-width:0.2px;mix-blend-mode:overlay;isolation:isolate;fill:url(#a);}.b{fill:url(#b);}.c,.d{fill:#fff;}.d{font-size:15px;font-family:SourceHanSansCN-Heavy, Source Han Sans CN;font-weight:800;}.e{stroke:none;}.f{fill:none;}.g{filter:url(#f);}.h{filter:url(#c);}</style>
  </defs>
  <g transform="matrix(1, 0, 0, 1, 4.2054443359375, 6.933380126953125)">
    <g data-type="innerShadowGroup">
      <rect class="b" width="20.265" height="20.265" rx="8" transform="translate(0 1)"/>
      <g class="g" transform="matrix(1, 0, 0, 1, -3, -2)" style="filter: url('#filter-1');">
        <rect class="c" width="20.265" height="20.265" rx="8" transform="translate(3 3)"/>
      </g>
    </g>
    <g class="f" transform="matrix(1, 0, 0, 1, -3, -2)" style="fill: rgb(0, 0, 0); filter: url('#filter-2');">
      <path class="c" d="M-9.015,0H-6.33V-3.7h1.3c2.355,0,4.41-1.17,4.41-3.825,0-2.76-2.025-3.63-4.485-3.63H-9.015ZM-6.33-5.82V-9.045h1.1c1.29,0,2.01.39,2.01,1.515,0,1.095-.63,1.71-1.935,1.71Z" transform="translate(18.13 19)"/>
    </g>
  </g>
</svg>