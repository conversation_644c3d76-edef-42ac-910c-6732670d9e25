<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="112" height="74" viewBox="0 0 112 74"><defs><style>.a{fill:url(#a);}.b{fill:#fff;}.c{fill:none;stroke:#1a41ad;stroke-width:0.7px;opacity:0.56;}.d{filter:url(#b);}</style><linearGradient id="a" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox"><stop offset="0" stop-color="#0a46ea"/><stop offset="1" stop-color="#235cf7"/></linearGradient><filter id="b"><feOffset dy="3" input="SourceAlpha"/><feGaussianBlur stdDeviation="5" result="c"/><feFlood flood-color="#84d2ef" flood-opacity="0.596" result="d"/><feComposite operator="out" in="SourceGraphic" in2="c"/><feComposite operator="in" in="d"/><feComposite operator="in" in2="SourceGraphic"/></filter></defs><g transform="translate(-7425.818 -6270)"><g data-type="innerShadowGroup"><rect class="a" width="112" height="74" rx="11" transform="translate(7425.818 6270)"/><g class="d" transform="matrix(1, 0, 0, 1, 7425.82, 6270)"><rect class="b" width="112" height="74" rx="11"/></g></g><line class="c" x2="108" transform="translate(7428.318 6306.916)"/></g></svg>