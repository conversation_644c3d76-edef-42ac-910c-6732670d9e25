<?xml version="1.0" encoding="utf-8"?>
<svg viewBox="27.2238 6.0355 20.265 20.265" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <radialGradient id="a" cx="0.5" cy="0.5" r="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#222d2d" stop-opacity="0.071"/>
      <stop offset="1" stop-color="#fff"/>
    </radialGradient>
    <linearGradient id="b" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#f5de2a"/>
      <stop offset="1" stop-color="#ed885b"/>
    </linearGradient>
    <filter id="c">
      <feOffset input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="1" result="d"/>
      <feFlood flood-color="#f9faf0" flood-opacity="0.902" result="e"/>
      <feComposite operator="out" in="SourceGraphic" in2="d"/>
      <feComposite operator="in" in="e"/>
      <feComposite operator="in" in2="SourceGraphic"/>
    </filter>
    <filter id="f" x="0" y="1.84" width="26.73" height="29.16" filterUnits="userSpaceOnUse">
      <feOffset dy="3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="3" result="g"/>
      <feFlood flood-opacity="0.302"/>
      <feComposite operator="in" in2="g"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <style>.a{stroke:#fff;stroke-width:0.2px;mix-blend-mode:overlay;isolation:isolate;fill:url(#a);}.b{fill:url(#b);}.c,.d{fill:#fff;}.d{font-size:15px;font-family:SourceHanSansCN-Heavy, Source Han Sans CN;font-weight:800;}.e{stroke:none;}.f{fill:none;}.g{filter:url(#f);}.h{filter:url(#c);}</style>
  </defs>
  <g transform="matrix(1, 0, 0, 1, 27.22381591796875, 5.03546142578125)">
    <g data-type="innerShadowGroup">
      <rect class="b" width="20.265" height="20.265" rx="8" transform="translate(0 1)"/>
      <g class="e" transform="matrix(1, 0, 0, 1, -3.03, -2)" style="filter: url('#c');">
        <rect class="c" width="20.265" height="20.265" rx="8" transform="translate(3.03 3)"/>
      </g>
    </g>
    <g class="d" transform="matrix(1, 0, 0, 1, -3.03, -2)" style="fill: rgb(0, 0, 0); filter: url('#f'); font-family: 'system-ui', -apple-system, Inter, Roboto, 'Helvetica Neue', 'Lucida Grande', Arial, sans-serif; font-weight: 400;">
      <path class="c" d="M-6.15,0h2.685V-8.94h3.03v-2.22h-8.73v2.22H-6.15Z" transform="translate(18.16 19)" style="font-family: 'system-ui', -apple-system, Inter, Roboto, 'Helvetica Neue', 'Lucida Grande', Arial, sans-serif; font-weight: 400;"/>
    </g>
  </g>
</svg>