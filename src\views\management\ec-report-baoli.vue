<template>
  <div class="ecReports-container">
    <div class="header filter-row">
      <div class="left">
        <div class="filter-item ">
          <div class="label">仪表</div>
          <el-select v-model="meter_type" size="mini" clearable @change="handleTypeChange">
            <el-option v-for="(item, index) in meterTypes" :key="index" :label="item" :value="item" />
          </el-select>
        </div>
        <div class="filter-item ">
          <el-input v-model="search" size="mini" placeholder="关键字" clearable @change="handleSearch">
            <el-button slot="append" icon="el-icon-search" @click="handleSearch" />
          </el-input>
        </div>
        <div class="filter-item  ">
          <div class="label">周期</div>
          <el-select v-model="sumTimeType" placeholder=" " size="mini" style="width: 150px" @change="handleTimeTypeChange">
            <el-option v-for="item in dateOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div class="filter-item">
          <div class="label">时间</div>
          <el-date-picker
            v-model="reportRange"
            :default-time="['00:00:00', '23:59:59']"
            :picker-options="pickerOptions"
            :type="sumTimeType === 'hr' || sumTimeType === 'di' ? 'daterange' : 'monthrange'"
            placeholder=""
            :clearable="false"
            size="mini"
            style="width: 200px"
            @change="handleSearch"
          />
          <!-- <el-date-picker
            v-model="dateValue"
            type="datetimerange"
            :picker-options="pickerOptions"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="handleSearch"
          /> -->
        </div>
      </div>
      <div class="right">
        <div class="filter-item ">
          <el-button type="success" size="mini" style="margin-right: 10px" @click="exportSummary">
            汇总导出
            <i :class="exportSummaryLoading ? 'el-icon-loading' : 'el-icon-download'" />
          </el-button>
        </div>
        <div class="filter-item ">
          <el-button type="primary" size="mini" @click="exportExcel">
            导出Excel
            <i :class="exportLoading ? 'el-icon-loading' : 'el-icon-download'" />
          </el-button>
        </div>
      </div>
    </div>
    <div ref="contentRef" v-loading="listLoading" class="content" element-loading-text="拼命加载中" element-loading-background="var(--theme-loading-mask)">
      <el-table
        v-if="!has_ppv_data"
        ref="multipleTable"
        :key="reportFlag"
        :max-height="tHeight"
        :data="filterTimeRange"
        style="width: 100%"
        default-expand-all
        :tree-props="{ children: 'children' }"
        row-key="id"
        fit
        border
      >
        <el-table-column align="center" label="名字" min-width="150" show-overflow-tooltip fixed>
          <template slot-scope="scope">{{ scope.row.name }}</template>
        </el-table-column>
        <!-- time_range -->
        <template v-for="(item, index) in filterReportLists">
          <el-table-column :key="index" :label="item.name" align="center" min-width="200">
            <el-table-column align="center" :label="meter_type === '' ? '仪表读数' : meter_type + '读数'" width="100" show-overflow-tooltip>
              <template slot-scope="scope">
                {{ item.pr_data[scope.row.data_index] == '--' ? item.pr_data[scope.row.data_index] : formatFloat(item.pr_data[scope.row.data_index], 2) }}
              </template>
            </el-table-column>
            <el-table-column align="center" label="能耗数值" width="100" show-overflow-tooltip>
              <template slot-scope="scope">
                {{ item.data[scope.row.data_index] == '--' ? item.data[scope.row.data_index] : formatFloat(item.data[scope.row.data_index], 2) }}
              </template>
            </el-table-column>
          </el-table-column>
        </template>
      </el-table>

      <!-- 有峰平谷尖的计算 -->
      <el-table
        v-else
        ref="multipleTable"
        :key="reportFlag"
        :header-cell-style="headerMethod"
        :span-method="spanMethod"
        :max-height="tHeight"
        :data="reportLists"
        :cell-style="specialCellStyle"
        :cell-class-name="specialCellClass"
        default-expand-all
        :tree-props="{ children: 'children' }"
        style="width: 100%"
        row-key="id"
        fit
        border
      >
        <el-table-column align="center" label="名字" prop="name" min-width="150" show-overflow-tooltip fixed>
          <template slot-scope="scope">{{ scope.row.name }}</template>
        </el-table-column>
        <el-table-column align="center" label="名字" prop="name" min-width="50" fixed>
          <template slot-scope="scope">
            <div v-for="(itm, idx) in scope.row.ppv_data" :key="idx" class="cell-block">
              {{ itm.ppv_type | ppvTypeFilter }}
            </div>
          </template>
        </el-table-column>
        <template v-for="(item, index) in time_range">
          <el-table-column :key="index" :label="item" align="center" min-width="200">
            <el-table-column align="center" :label="meter_type === '' ? '仪表读数' : meter_type + '读数'" width="100">
              <template slot-scope="scope">
                <div v-for="(itm, idx) in scope.row.ppv_data" :key="idx" class="cell-block">
                  {{ itm.pr_data[index] == '--' ? itm.pr_data[index] : formatFloat(itm.pr_data[index], 2) }}
                </div>
              </template>
            </el-table-column>
            <el-table-column align="center" label="能耗数值" width="100">
              <template slot-scope="scope">
                <div v-for="(itm, idx) in scope.row.ppv_data" :key="idx" class="cell-block">
                  {{ itm.data[index] == '--' ? itm.data[index] : formatFloat(itm.data[index], 2) }}
                </div>
              </template>
            </el-table-column>
          </el-table-column>
        </template>
      </el-table>
      <div class="footer">
        <el-pagination
          :current-page="page"
          :page-size="perPage"
          :page-sizes="[10, 20, 40, 100]"
          :total="total"
          background
          :layout="projectId == 74 ? 'total' : 'total,sizes, prev, pager, next, jumper'"
          @current-change="currentPage"
          @next-click="nextPage"
          @prev-click="prePage"
          @size-change="sizeChange"
        />
      </div>
    </div>

    <el-dialog title="用量计算" :visible.sync="ecCalculationVisible" :modal-append-to-body="false">
      <el-form label-width="80px">
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="ecCalculationDate"
            :type="ecCalculationDateType"
            range-separator="至"
            unlink-panels
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :picker-options="pickerOptions"
          />
        </el-form-item>
        <el-form-item label="数据颗粒度">
          <el-radio-group v-model="ecCalculationType">
            <el-radio-button v-for="(item, index) in ecTypeOptions" :key="index" :value="item.value" :label="item.value">{{ item.name }}</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="getEcConsumeCalc">执行计算</el-button>
        </el-form-item>
        <el-table :data="ecCalculation">
          <el-table-column prop="location" label="" width="180" />
          <el-table-column prop="ratio" label="分摊比例（%）" />
          <el-table-column prop="cool" label="用冷量（kWh）" />
          <el-table-column prop="power" label="用电量（kWh）" />
          <el-table-column prop="gas" label="用气量（m³）" />
        </el-table>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import { exportEcReport, getEcConsumeCalc, getEtReports } from '@/api/ec_dashboard'
import { mapGetters } from 'vuex'
import { getOneDayAgo, parseTime, vhToPixels } from '@/utils'
import { getProject } from '@/utils/auth'
import { bao_li_report } from '@/utils/report-filter'

export default {
  name: 'EcReports',
  filters: {
    timeFilter(val) {
      return parseTime(val)
    },
    ppvTypeFilter(val) {
      const ppv_types = [{ name: '总', value: 0 }, { name: '峰', value: 10 }, { name: '平', value: 20 }, { name: '谷', value: 30 }, { name: '尖', value: 40 }]
      const res = ppv_types.filter(item => {
        return item.value == val
      })
      return res[0].name
    }
  },
  data() {
    const today = new Date()
    const startOfDay = new Date(today.setHours(0, 0, 0, 0))
    const endOfDay = new Date(today.setHours(23, 59, 59, 999))
    return {
      baseUrl: process.env.VUE_APP_BASE_API,
      total: 0,
      page: 1,
      perPage: 40,
      search: '',
      searchVal: '',
      listLoading: true,
      exportLoading: false,
      reportLists: [],
      filterReportLists: [], // 保利的表格样式转换过滤数据
      filterTimeRange: [],
      time_range: [],
      tableHeight: 0,
      tableRowHeight: 0,
      meterTypes: [],
      meter_type: '',
      // ecTypes: JSON.parse(localStorage.getItem('ecTypes')),
      // ec_type: 10,
      dateOptions: [{ label: '小时', value: 'hr' }, { label: '日', value: 'di' }, { label: '月', value: 'mo' }],
      sumTimeType: 'hr',
      sumDate: new Date(),
      reportRange: [startOfDay, endOfDay],
      pickerOptions: {
        // disabledDate(time) {
        //   return time.getTime() > Date.now() - 8.64e6
        // }
      },
      reportFlag: false,
      dateValue: [],
      timeRange: {},

      // summary data export
      exportSummaryLoading: false,
      summaryData: [],
      tHeight: 0,

      allReportData: [],

      // ec calculation
      ecCalculationVisible: false,
      ecCalculationDate: [getOneDayAgo(), new Date()],
      ecCalculationType: 'hr',
      ecTypeOptions: [{ name: '时', value: 'hr' }, { name: '日', value: 'di' }, { name: '月', value: 'mo' }],
      ecCalculation: [],

      // 峰平谷尖计算
      en_ppv: false,
      has_ppv_data: false,
      ppv_types: [{ name: '总', value: 0 }, { name: '峰', value: 10 }, { name: '平', value: 20 }, { name: '谷', value: 30 }, { name: '尖', value: 40 }] // 0-总，10-峰，20-平，30-谷，40-尖
    }
  },
  computed: {
    ...mapGetters(['enableAutoRefresh', 'permissions']),
    projectId() {
      return getProject()
    },
    isTableScroll() {
      if (this.reportLists.length) {
        // table header always be 7vh
        return this.tableHeight < (this.reportLists.length + 1) * this.tableRowHeight
      }
      return false
    },
    ecCalculationDateType() {
      let result = 'datetimerange'
      if (this.ecCalculationType === 'di') {
        result = 'daterange'
      } else if (this.ecCalculationType === 'mo') {
        result = 'month'
      }
      return result
    }
  },
  created() {
    const en_ppv = JSON.parse(localStorage.getItem('enPpv'))
    this.en_ppv = en_ppv

    this.switchPage()
  },
  mounted() {
    const { height } = this.$refs.contentRef.getBoundingClientRect()
    this.tableHeight = height
    this.tableRowHeight = vhToPixels(7)
    this.$refs.multipleTable.bodyWrapper.addEventListener(
      'scroll',
      res => {
        this.$refs.multipleTable.doLayout()
      },
      true
    )
    const that = this
    this.$nextTick(() => {
      this.tHeight = this.$refs.contentRef.offsetHeight
    })
    window.addEventListener('resize', function() {
      that.$nextTick(() => {
        that.tHeight = that.$refs.contentRef.offsetHeight
      })
    })
  },
  methods: {
    headerMethod({ row, column, rowIndex, columnIndex }) {
      if (row[0].level == 1) {
        row[0].colSpan = 0
        row[1].colSpan = 2
        if (columnIndex === 0) {
          return { display: 'none' }
        }
      }
    },
    // 合并列
    spanMethod({ row, column, rowIndex, columnIndex }) {
      // if (columnIndex === 0) {
      //   if (column.property === 'name') {
      //     if (rowIndex !== 0) {
      //       return { display: 'none' }
      //     } else {
      //       return { rowspan: 5, colspan: 1 }
      //     }
      //   } else {
      //     return { rowspan: 1, colspan: 1 }
      //   }
      // }
    },
    specialCellStyle({ row, column, rowIndex, columnIndex }) {
      if ((rowIndex != 0 || rowIndex != 1) && columnIndex != 0) {
        return {
          padding: '0px'
        }
      }
    },
    specialCellClass({ row, column, rowIndex, columnIndex }) {
      if ((rowIndex != 0 || rowIndex != 1) && columnIndex != 0) {
        return 'special-cell'
      }
    },

    getLastFourDigits(name) {
      // const match = name.match(/(\d{4})$/)
      // return match ? match[1] : '' //提取数字
      const match = name.slice(-4)
      return match // 提取字符不限于数字
    },
    switchPage() {
      this.listLoading = true
      const params = {
        per_page: 999,
        page: 1,
        search: this.search || undefined,
        meter_type: this.meter_type || undefined,
        type: this.sumTimeType,
        // dt: parseTime(this.sumDate, '{y}{m}{d}000000'),
        from: parseTime(this.reportRange[0], '{y}{m}{d}{h}{i}{s}'),
        till: parseTime(this.reportRange[1], '{y}{m}{d}{h}{i}{s}')
      }

      getEtReports(params)
        .then(res => {
          let stats = res.data.stats
          if (this.projectId == 74) {
            // 根据bao_li_report数组筛选名字最后4位是否匹配
            stats = res.data.stats.filter(item => {
              const lastFour = this.getLastFourDigits(item.name)
              return bao_li_report.some(report => {
                const flag = this.getLastFourDigits(report.name) && this.getLastFourDigits(report.name) === lastFour
                return flag
              })
            })
          }
          this.time_range = res.data.time_range
          this.meterTypes = res.data.meter_types

          if (!this.en_ppv) {
            this.has_ppv_data = false
            this.reportLists = JSON.parse(JSON.stringify(stats))

            // 重新筛选，表头为name,纵行第一列为时间
            const range = [...res.data.time_range, '累计值', '最大值', '最小值', '平均值']
            this.filterReportLists = JSON.parse(JSON.stringify(stats)).map((item, index) => {
              const length1 = item.data.filter(item => item != '--').length
              const length2 = item.pr_data.filter(item => item != '--').length
              // data
              const cum1 = item.data.reduce((pre, cur) => pre + (cur != '--' ? cur : 0), 0).toFixed(2)
              const max1 = Math.max(...item.data.filter(item => item != '--'))
              const min1 = Math.min(...item.data.filter(item => item != '--'))
              const avg1 = (cum1 / length1).toFixed(2)
              // pr_data
              const cum2 = item.pr_data.reduce((pre, cur) => pre + (cur != '--' ? cur : 0), 0).toFixed(2)
              const max2 = Math.max(...item.pr_data.filter(item => item != '--'))
              const min2 = Math.min(...item.pr_data.filter(item => item != '--'))
              const avg2 = (cum2 / length2).toFixed(2)
              item.data = [...item.data, cum1, max1, min1, avg1]
              item.pr_data = [...item.pr_data.slice(0, item.pr_data.length - 1), cum2, max2, min2, avg2]
              return item
            })
            this.filterTimeRange = range.map((item, index) => {
              const res = {
                data_index: index,
                name: item
              }
              return res
            })
          } else {
            this.has_ppv_data = true
            this.reportLists = stats.map(item => {
              item = this.statsLoop(item)
              return item
            })
          }
          // 保利条数 选筛选过后的总数
          this.total = this.reportLists.length
        })
        .finally(() => {
          this.reportFlag = !this.reportFlag
          this.listLoading = false
        })
    },
    statsLoop(item) {
      const obj = item
      // obj.hasChildren = false
      if (obj.pr_data && obj.data) {
        obj.ppv_data.splice(0, 0, {
          ppv_type: 0,
          data: obj.data,
          pr_data: obj.pr_data
        })
      }
      obj.ppv_data.map(itm => {
        itm = Object.assign(itm, {
          id: obj.id,
          identifier: obj.identifier,
          meter_type: obj.meter_type,
          name: obj.name
        })
      })
      if (obj.children && obj.children.length > 0) {
        // obj.hasChildren = true
        obj.children = obj.children.map((itm, index) => {
          return this.statsLoop(itm)
        })
      }
      return obj
    },
    getAllExpandChildren(item) {
      this.allReportData.push(item)
      if (item.children && item.children.length > 0) {
        item.children.forEach(itm => {
          this.getAllExpandChildren(itm)
        })
      }
    },
    currentPage(val) {
      this.page = val
      this.switchPage()
    },
    nextPage() {
      this.page++
      this.switchPage()
    },
    prePage() {
      this.page--
      this.switchPage()
    },
    sizeChange(val) {
      this.page = 1
      this.perPage = val
      this.page = 1
      this.switchPage()
    },
    handleSearch() {
      this.page = 1
      this.switchPage()
    },
    handleTimeTypeChange() {
      const now = new Date()
      if (this.sumTimeType === 'hr') {
        const startOfDay = new Date(now.setHours(0, 0, 0, 0))
        const endOfDay = new Date(now.setHours(23, 59, 59, 999))
        this.reportRange = [startOfDay, endOfDay]
      } else if (this.sumTimeType === 'di') {
        const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1, 0, 0, 0, 0)
        const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59, 999)
        this.reportRange = [startOfMonth, endOfMonth]
      } else if (this.sumTimeType === 'mo') {
        const startOfYear = new Date(now.getFullYear(), 0, 1, 0, 0, 0, 0)
        const endOfYear = new Date(now.getFullYear(), 11, 31, 23, 59, 59, 999)
        this.reportRange = [startOfYear, endOfYear]
      }
      this.handleSearch()
    },
    handleTypeChange(val) {
      this.page = 1
      this.switchPage()
    },
    handlePageChange(val) {},

    exportExcel() {
      this.exportLoading = true
      this.allReportData = []

      const params = {
        per_page: 9999,
        page: 1,
        search: this.search || undefined,
        meter_type: this.meter_type || undefined,
        type: this.sumTimeType,
        // dt: parseTime(this.sumDate, '{y}{m}{d}000000')
        from: parseTime(this.reportRange[0], '{y}{m}{d}{h}{i}{s}'),
        till: parseTime(this.reportRange[1], '{y}{m}{d}{h}{i}{s}')
      }
      let reportLists = []
      // let time_range = []

      // 重新筛选，表头为name,纵行第一列为时间
      // const filterTimeRange = this.filterTimeRange
      const filterReportLists = this.filterReportLists

      getEtReports(params)
        .then(res => {
          let stats = res.data.stats
          if (this.projectId == 74) {
            // 根据bao_li_report数组筛选名字最后4位是否匹配
            stats = res.data.stats.filter(item => {
              const lastFour = this.getLastFourDigits(item.name)
              return bao_li_report.some(report => {
                const flag = this.getLastFourDigits(report.name) && this.getLastFourDigits(report.name) === lastFour
                return flag
              })
            })
          }
          reportLists = stats
          if (this.has_ppv_data) {
            reportLists = this.reportLists
          }
          reportLists.forEach(item => {
            this.getAllExpandChildren(item)
          })
          reportLists = this.allReportData

          // time_range = res.data.time_range
        })
        .then(() => {
          const header1 = ['名字']
          const header2 = ['名字']
          const tHeader = ['名字']
          const filterVal = ['name']

          let merges = [
            // 'A1:A2', 'B1:C1',
            'D1:E1',
            'F1:G1',
            'H1:I1',
            'J1:K1',
            'L1:M1',
            'N1:O1',
            'P1:Q1',
            'R1:S1',
            'T1:U1',
            'V1:W1',
            'X1:Y1',
            'Z1:AA1',
            'AB1:AC1',
            'AD1:AE1',
            'AF1:AG1',
            'AH1:AI1',
            'AJ1:AK1',
            'AL1:AM1',
            'AN1:AO1',
            'AP1:AQ1',
            'AR1:AS1',
            'AT1:AU1',
            'AV1:AW1',
            'AX1:AY1',
            'AZ1:BA1',
            'BB1:BC1',
            'BD1:BE1',
            'BF1:BG1',
            'BH1:BI1',
            'BJ1:BK1',
            'BL1:BM1',
            'BN1:BO1',
            'BP1:BQ1',
            'BR1:BS1',
            'BT1:BU1',
            'BV1:BW1',
            'BX1:BY1',
            'BZ1:CA1',
            'CB1:CC1'
          ] // 需要合并的单元格

          if (this.has_ppv_data) {
            const merges2 = ['A1:C2']
            header1.push('峰平谷尖', '峰平谷尖')
            header2.push('峰平谷尖', '峰平谷尖')
            filterVal.push('峰平谷尖', 'ppv')
            let i1 = 3
            let i2 = 7
            reportLists.forEach((itm, idx) => {
              merges2.push(`A${i1}:B${i2}`)
              i1 += 5
              i2 += 5
            })
            merges = merges.concat(merges2)
          } else merges = merges.concat(['A1:A2', 'B1:C1'])

          // 重新筛选，表头为name,纵行第一列为时间

          filterReportLists.forEach((item, index) => {
            header1.push(item.name, '')
            header2.push('仪表读数', '能耗数值')
            filterVal.push(`仪表读数${index}`, `能耗数值${index}`)
            tHeader.push('仪表读数', '能耗数值')
          })
          // 需要返回一个数组["A1:B1","C1:D1"]
          // 其中A代表第一列，1代表第一行，"A1:B1"可以在表格中看到就是左上角两个，他们两个进行合并；
          import('@/vendor/Export2Excel')
            .then(excel => {
              // const list = reportLists
              const list = this.has_ppv_data ? reportLists : filterReportLists
              const fileName = this.meter_type === '' ? `能耗报表` : `能耗报表(${this.meter_type})`
              const data = this.has_ppv_data ? this.formatJson2(filterVal, list) : this.formatJson(filterVal, list)
              excel.export_json_to_excel({
                multiHeader: [header1, header2],
                // header: tHeader,
                data,
                merges: merges,
                filename: fileName || 'excel-file',
                autoWidth: true,
                bookType: 'xlsx'
              })
            })
            .catch(err => {
              console.log(err)
            })
            .finally(() => {
              this.exportLoading = false
            })
        })
    },

    exportSummary() {
      this.exportSummaryLoading = true
      const params = {
        page: 1,
        per_page: 9999,
        search: this.search,
        meter_type: this.meter_type,
        type: 'yr',
        dt: parseTime(this.sumDate, '{y}{m}{d}000000')
      }

      Object.keys(params).forEach(key => {
        if (!params[key]) {
          delete params[key]
        }
      })

      exportEcReport(params)
        .then(result => {
          const { filename, file_url } = result.data
          if (!file_url) {
            this.$message.warning('无数据！')
          } else {
            const a = document.createElement('a')
            a.style.display = 'none'
            // a.href = `${this.baseUrl}/${encodeURIComponent(result.data.file_url)}`
            a.href = `${this.baseUrl}/${file_url}`
            a.download = filename + '.xlsx'
            a.style.display = 'none'
            document.body.appendChild(a)
            a.click()
            document.body.removeChild(a)
          }
        })
        .finally(() => {
          this.exportSummaryLoading = false
        })
    },
    handleEcConsumeCalc() {
      this.ecCalculationVisible = true
    },
    getEcConsumeCalc() {
      this.ecCalculation = []

      const params = {
        type: this.ecCalculationType
        // from: parseTime(this.ecCalculationDate[0], '{y}{m}{d}{h}{i}{s}'),
        // till: parseTime(this.ecCalculationDate[1], '{y}{m}{d}{h}{i}{s}')
      }
      if (this.ecCalculationType === 'mo') {
        params['from'] = parseTime(this.ecCalculationDate, '{y}{m}{d}{h}{i}{s}')
        params['till'] = parseTime(this.ecCalculationDate, '{y}{m}{d}{h}{i}{s}')
      } else {
        params['from'] = parseTime(this.ecCalculationDate[0], '{y}{m}{d}{h}{i}{s}')
        params['till'] = parseTime(this.ecCalculationDate[1], '{y}{m}{d}{h}{i}{s}')
      }
      getEcConsumeCalc(params).then(result => {
        const data = result.data
        this.ecCalculation.push({
          location: '游泳池',
          ratio: data.ratio.pool,
          cool: data.cool_stats.pool.toFixed(2),
          power: data.power_stats.pool.toFixed(2),
          gas: data.gas_stats.pool.toFixed(2)
        })
        this.ecCalculation.push({
          location: '健身中心',
          ratio: data.ratio.gym.toFixed(2),
          cool: data.cool_stats.gym.toFixed(2),
          power: data.power_stats.gym.toFixed(2),
          gas: data.gas_stats.gym.toFixed(2)
        })
      })
    },
    formatJson(filterVal, jsonData) {
      // 重新筛选，表头为name,纵行第一列为时间
      const filterTimeRange = this.filterTimeRange
      const res = filterTimeRange.map((item, index) =>
        filterVal.map((j, idx) => {
          if (j == 'name') {
            return item.name
          } else if (j.includes('仪表读数')) {
            return jsonData[j.slice(4, j.length)].pr_data[index]
          } else if (j.includes('能耗数值')) {
            return jsonData[j.slice(4, j.length)].data[index]
          }
        })
      )
      return res
    },
    formatJson2(filterVal, jsonData) {
      const ppv_title = ['总', '峰', '平', '谷', '尖']
      const res = []
      let count = 0
      jsonData.forEach((itm, index) => {
        itm.ppv_data.forEach((v, i) => {
          res.push([])
          filterVal.forEach((j, idx) => {
            if (j === 'name') {
              res[count].push(itm.name)
            } else if (j === 'ppv') {
              res[count].push(ppv_title[i])
            } else if (j.includes('仪表读数')) {
              res[count].push(v.pr_data[j.slice(4, j.length)])
            } else if (j.includes('能耗数值')) {
              res[count].push(v.data[j.slice(4, j.length)])
            } else res[count].push(undefined)
          })
          count++
        })
      })
      // console.log(filterVal, jsonData, res)
      return res
    },
    formatTime(val) {
      return parseTime(new Date(val), '{y}-{m}-{d}')
    },
    formatFloat(src, pos) {
      return Math.round(src * Math.pow(10, pos)) / Math.pow(10, pos)
    }
  }
}
</script>

<style lang="scss" scoped>
.ecReports-container {
  width: 100%;
  height: 100%;
  // padding: 2% 3.5% 0;
  padding: 1% 1% 1% 1%;
  // background: #3c4246;
  background: var(--theme-bg-color);
  color: #999999;

  .filter-row {
    height: 50px;
    padding: 15px;
    background: var(--header-block) !important;
    box-shadow: inset 0 0 20px 20px var(--header-inset-shadow) !important;
    background-size: 100% 100% !important;
    border-radius: 4px;
    /deep/.el-input-group__append {
      background-color: var(--el-input-box-append) !important;
      color: var(--content-text-color);
      border: none !important;
    }
    .filter-item {
      display: flex;
      align-items: center;
      height: 28px;
      justify-content: space-between;

      .label {
        display: inline-block;
        //height: 28px !important;
        line-height: 28px;
        font-size: 0.875rem;
        width: 80px;
        text-align: center;
        color: var(--label-inside-color);
        background: var(--label-bg-color);
        border-radius: 4px 0 0 4px;
      }

      .active-label {
        color: #fefefe;
      }
    }

    .left {
      display: flex;

      align-items: center;
      .filter-item {
        margin-right: 1rem;
      }
    }
    .right {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      .filter-item {
        //margin-left: 10px;
      }
    }
  }
}
</style>

<style lang="scss">
.ecReports-container {
  .filter-row {
    display: flex;
    align-items: center;
    height: 30px;
    .el-input {
      input {
        border-radius: 4px 0 0 4px;
      }
    }
    .el-select {
      input {
        border-radius: 0 4px 4px 0;
      }
    }
    .el-date-editor {
      input {
        border-radius: 0 4px 4px 0;
      }
    }
  }
  .content {
    .scroll-table {
      height: 100%;
    }
    .short-table {
      max-height: 100%;
    }
    .el-table__body-wrapper {
      // height: calc(100% - 14vh);
      width: 100%;
      .el-scrollbar {
        .el-scrollbar__wrap {
          overflow-x: hidden;
        }
      }
    }
    .el-table__fixed {
      height: 100% !important;
    }
    .el-table__fixed-body-wrapper {
      overflow-y: auto; // fixed的列也要超出滚动
      // pointer-events: none; // 禁止左侧固定列滑动
      // cursor: default;
    } //固定列滑动问题解决
    .el-scrollbar__thumb {
      background: #ffffffc3;
      z-index: 9999;
    }
    .el-scrollbar__bar {
      z-index: 9999;
    }

    .el-loading-mask {
      border-radius: 10px;
    }
  }

  .el-scrollbar__wrap {
    overflow-x: hidden;
  }
  .el-textarea__inner {
    // el-input输入时设置边框颜色
    background-color: #fff;
    color: #7b8d9c;
    font-size: 1rem;
  }
  .el-input__count {
    background-color: transparent;
  }
  .cell-wrap {
    display: flex;
    flex-wrap: nowrap;
    justify-content: center;

    .el-button + .el-button {
      margin-left: 0px;
    }

    .cell-action.el-button {
      margin: 0 10px;
    }
  }

  .footer {
    margin-top: 15px;
    display: flex;
    justify-content: center;
  }
  .el-table thead.is-group th {
    background: var(--th-bg) !important;
  }
  .el-table--border th {
    border-bottom: 1px solid var(--table-border-color);
  }

  .el-table {
    .cell-block {
      display: flex;
      align-items: center;
      justify-content: center;
      border-bottom: 1px solid var(--linkage-border);
      padding: 10px;
      height: 46px;
    }
    .special-cell {
      .cell {
        padding-left: 0px;
        padding-right: 0px;
      }
    }

    .cell-block:last-child {
      border-bottom: 0px;
    }
    .cell.el-tooltip {
      white-space: wrap;
    }
  }
}
</style>

<style lang="scss" scoped>
.ecReports-container {
  width: 100%;
  height: 100%;
  // padding: 2% 3.5% 0;
  padding: 1% 1% 1% 1%;
  // background: #3c4246;
  background: var(--theme-bg-color);
  color: #999999;

  .content {
    height: calc(100% - 135px);
    margin-top: 10px;
  }
  .footer {
    margin-top: 15px;
    display: flex;
    justify-content: center;
  }
}
</style>
