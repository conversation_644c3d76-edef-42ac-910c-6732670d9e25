import { getGroup } from '@/api/group'

let widgets = null
// 设备分组控件
async function fetchDeviceGroups() {
  const res = await getGroup({
    page: 1,
    pageSize: 100
  })
  const groups = res.data.groups || []
  const deviceGroups = groups.map((item, idx) => ({
    groupId: item.id,
    // device_prototypes: item.device_prototypes,
    dp_ids: item.device_prototypes.map(item => item.id),
    name: item.name,
    type: 'table_GroupTable',
    width: 540,
    height: 190,
    fontSize: 18,
    stroke: '#000000',
    strokeW: 1,
    opacity: 100,
    fontColor: '#ffff',
    bgColor: 'transparent',
    borderColor: '#fff',
    value: '',
    align: 'center',
    unit: ''
  }))
  return deviceGroups
}

async function initializeWidgets() {
  if (!widgets) {
    const deviceGroups = await fetchDeviceGroups()
    widgets = [
      {
        groupName: '文本',
        widgets: [
          {
            name: '纯文本',
            type: 'svg_PlainText',
            width: 100,
            height: 20,
            fontSize: 14,
            fontColor: 'white',
            bgColor: 'transparent',
            gradientColor: '',
            value: '纯文本',
            align: 'middle',
            radius: 3,
            valign: 'middle'
          },
          {
            name: '参数文本',
            type: 'svg_ParamText',
            width: 100,
            height: 20,
            fontSize: 14,
            radius: 3,
            fontColor: 'white',
            bgColor: '#1ca085',
            value: '26',
            align: 'middle',
            valign: 'middle',
            unit: '',
            currentUnit: '',
            showUnit: true,
            condition: 'true',
            trueBgColor: '#1ca085',
            trueFontColor: 'white',
            trueText: '',
            falseBgColor: '#c0382b',
            falseFontColor: '#f3f3f3',
            falseText: '',
            trueGradientColor: '',
            falseGradientColor: ''
          }
        ]
      },
      {
        groupName: '图形',
        widgets: [
          {
            name: '矩形',
            type: 'svg_Rectangle',
            width: 80,
            height: 40,
            stroke: '#000000',
            strokeW: 1,
            fill: '#6d9eeb',
            opacity: 100,
            round: 0
          },
          {
            name: '三角形',
            type: 'svg_Triangle',
            width: 40,
            height: 40,
            stroke: '#000000',
            strokeW: 1,
            fill: '#6d9eeb',
            opacity: 100
          }
        ]
      },
      {
        groupName: '参数图标',
        widgets: [
          {
            name: '进度条',
            type: 'vue_ProgressBar',
            width: 100,
            height: 20,
            radius: 2,
            value: 60,
            progressColor: '#1FBC9C',
            bgColor: '#2980B9',
            texture: '颜色',
            fontColor: 'white',
            fontSize: 13,
            fullValue: 100
          },
          {
            name: '水塔',
            type: 'vue_WaterTank',
            width: 60,
            height: 50,
            fontSize: 12,
            fontColor: '#ffffff',
            thresholdUp: 40,
            thresholdLow: 10,
            tankHeight: 50,
            value: 20
          },

          {
            name: '子设备详情',
            type: 'vue_DeviceArea',
            width: 80,
            height: 50,
            bgColor: '#2e7bbf',
            prefix: '',
            idx: 1
          },
          {
            name: '报警',
            type: 'vue_Alarm',
            width: 20,
            height: 20,
            url: require('@/assets/images/floor/alarm.gif'),
            flickering: true,
            condition: 'true'
          },
          {
            name: '故障',
            type: 'vue_Fault',
            width: 20,
            height: 20,
            url: require('@/assets/images/floor/fault.gif'),
            flickering: true,
            condition: 'true'
          },
          {
            name: '设备参数',
            type: 'vue_ParamBubble',
            width: 60,
            height: 60,
            rotate: 45,
            fontSize: 14,
            fontColor: 'white',
            bgColor: '#4a86e8',
            unit: '',
            value: 26
          },
          {
            name: '运行状态',
            type: 'vue_RunningIndicator',
            width: 20,
            height: 20,
            condition: 'value === "运行"',
            value: '运行'
          },
          {
            name: '远程状态',
            type: 'vue_RemoteStatus',
            width: 20,
            height: 20,
            value: '远程'
          },
          {
            name: '帮助信息',
            type: 'vue_Help',
            width: 20,
            height: 20,
            fontSize: 16,
            text: ''
          },

          {
            name: 'tab跳转',
            type: 'vue_TabSwitcher',
            width: 60,
            height: 20,
            bgColor: '#093EEF',
            fontColor: 'white',
            radius: 4,
            fontSize: 12,
            text: '跳转至',
            targetTab: ''
          },

          {
            name: '外链跳转',
            type: 'vue_Link',
            width: 20,
            height: 20,
            url: ''
          },
          {
            name: '颜色',
            type: 'vue_ColorfulBlock',
            width: 15,
            height: 15,
            trueColor: '#980000',
            falseColor: '',
            flickering: false,
            condition: 'true',
            speed: 3,
            opacity: 40,
            radius: 4
          }
        ]
      },
      {
        groupName: '无参数控件',
        widgets: [
          {
            name: '箭头',
            type: 'vue_Arrow',
            colorStart: '#52D7F7',
            colorEnd: '#1381bb',
            width: 100,
            height: 20
          },
          {
            name: '箭头2',
            type: 'vue_ArrowDouble',
            colorStart1: '#52D7F7',
            colorEnd1: '#1381bb',
            colorStart2: '#F82959',
            colorEnd2: '#F82959',
            width: 100,
            height: 20
          },
          {
            name: '天气1',
            type: 'icon_Svg',
            icon: 'weather1',
            width: 100,
            height: 30
          },
          {
            name: '天气2',
            type: 'icon_Svg',
            icon: 'weather2',
            width: 100,
            height: 30
          },
          {
            name: '标准背景1',
            type: 'icon_Svg',
            icon: 'std_bg1',
            width: 70,
            height: 40
          },
          {
            name: '标准背景2',
            type: 'icon_Svg',
            icon: 'std_bg2',
            width: 70,
            height: 40
          },
          {
            name: '标准背景3',
            type: 'icon_Svg',
            icon: 'std_bg3',
            width: 70,
            height: 40
          },
          {
            name: '标准背景4',
            type: 'icon_Svg',
            icon: 'std_bg4_merge',
            width: 70,
            height: 40
          },
          {
            name: '功率频率',
            type: 'icon_Svg',
            icon: 'power_freq',
            width: 70,
            height: 40
          },
          {
            name: '百分比',
            type: 'icon_Svg',
            icon: 'percent',
            width: 70,
            height: 40
          },
          {
            name: '温度',
            type: 'icon_Svg',
            icon: 'temperature',
            width: 70,
            height: 40
          },
          {
            name: '压力',
            type: 'icon_Svg',
            icon: 'pressure',
            width: 70,
            height: 40
          },
          {
            name: '温度/压力',
            type: 'icon_Svg',
            icon: 'temp_press',
            width: 100,
            height: 40
          },
          {
            name: '板换',
            type: 'icon_Svg',
            icon: 'heat_exchanger',
            width: 30,
            height: 30
          },
          {
            name: '连线',
            type: 'svg_ConnectionLine',
            bgColor: '#3c78d8',
            width: 3,
            animation: '无',
            animaColor: '',
            style: '实线',
            animaDur: 5,
            points: [],
            radius: 10,
            pending: null,
            // 线段开始的端点样式
            ms: 'line',
            // 线段结束的端点样式
            me: 'line'
          },
          {
            name: '指北针1',
            type: 'icon_Svg',
            icon: 'compass_1',
            width: 30,
            height: 30
          },
          {
            name: '指北针2',
            type: 'icon_Svg',
            icon: 'compass_2',
            width: 30,
            height: 30
          },
          {
            name: 'P',
            type: 'icon_Svg',
            icon: 'p',
            height: 30,
            width: 30
          },
          {
            name: 'T',
            type: 'icon_Svg',
            icon: 't',
            width: 30,
            height: 30
          },
          {
            name: 'P2',
            type: 'icon_Svg',
            icon: 'p2',
            height: 30,
            width: 30
          },
          {
            name: 'T2',
            type: 'icon_Svg',
            icon: 't2',
            width: 30,
            height: 30
          },
          {
            name: '温度图标',
            type: 'icon_Svg',
            icon: 'temp',
            width: 30,
            height: 30
          },
          {
            name: '湿度图标',
            type: 'icon_Svg',
            icon: 'humi',
            width: 30,
            height: 30
          },

          // 管道控件
          {
            name: '管道1',
            type: 'icon_PipeLine',
            icon: 'pipeline1',
            width: 70,
            height: 40,
            color: 'green',
            colorStart: '#00d025',
            colorEnd: '#00d025',
            portColor: '#00d025',
            lightColor: '#fff'
          },
          {
            name: '管道2',
            type: 'icon_PipeLine',
            icon: 'pipeline2',
            width: 70,
            height: 40,
            color: 'green',
            colorStart: '#00d025',
            colorEnd: '#00d025',
            portColor: '#00d025',
            lightColor: '#fff'
          },
          {
            name: '管道3',
            type: 'icon_PipeLine',
            icon: 'pipeline3',
            width: 70,
            height: 40,
            color: 'green',
            colorStart: '#00d025',
            colorEnd: '#00d025',
            portColor: '#00d025',
            lightColor: '#fff'
          },
          {
            name: '管道4',
            type: 'icon_PipeLine',
            icon: 'pipeline4',
            width: 70,
            height: 40,
            color: 'green',
            colorStart: '#00d025',
            colorEnd: '#00d025',
            portColor: '#00d025',
            lightColor: '#fff'
          },
          {
            name: '管道5',
            type: 'icon_PipeLine',
            icon: 'pipeline5',
            width: 70,
            height: 40,
            color: 'green',
            colorStart: '#00d025',
            colorEnd: '#00d025',
            portColor: '#00d025',
            lightColor: '#fff'
          },
          {
            name: '管道6',
            type: 'icon_PipeLine',
            icon: 'pipeline6',
            width: 70,
            height: 40,
            color: 'green',
            colorStart: '#00d025',
            colorEnd: '#00d025',
            portColor: '#00d025',
            lightColor: '#fff'
          },
          {
            name: '管道7',
            type: 'icon_PipeLine',
            icon: 'pipeline7',
            width: 70,
            height: 40,
            color: 'green',
            colorStart: '#00d025',
            colorEnd: '#00d025',
            portColor: '#00d025',
            lightColor: '#fff'
          },
          {
            name: '管道8',
            type: 'icon_PipeLine',
            icon: 'pipeline8',
            width: 70,
            height: 40,
            color: 'green',
            colorStart: '#00d025',
            colorEnd: '#00d025',
            portColor: '#00d025',
            lightColor: '#fff'
          },
          {
            name: '管道9',
            type: 'icon_PipeLine',
            icon: 'pipeline9',
            width: 70,
            height: 40,
            color: 'green',
            colorStart: '#00d025',
            colorEnd: '#00d025',
            portColor: '#00d025',
            lightColor: '#fff'
          },
          {
            name: '管道10',
            type: 'icon_PipeLine',
            icon: 'pipeline10',
            width: 70,
            height: 40,
            color: 'green',
            colorStart: '#00d025',
            colorEnd: '#00d025',
            portColor: '#00d025',
            lightColor: '#fff'
          }
        ]
      },
      {
        groupName: '控制控件',
        widgets: [
          {
            name: '开关',
            type: 'vue_CtrlSwitch',
            width: 50,
            height: 20,
            dataType: 10,
            activeColor: '#3B74F4',
            inactiveColor: '#DCDFE5',
            value: true
          },
          {
            name: '数值',
            type: 'vue_CtrlNumber',
            width: 50,
            height: 20,
            dataType: 10,
            accentColor: '#3B74F4',
            bgColor: 'transparent',
            fontColor: 'white',
            value: 0
          },
          {
            name: '枚举',
            type: 'vue_CtrlSelector',
            width: 50,
            height: 30,
            dataType: 30,
            accentColor: '#3B74F4',
            bgColor: 'transparent',
            fontColor: 'white',
            value: '远程'
          }
        ]
      },
      {
        groupName: '设备图标',
        widgets: [
          {
            name: '风机盘管',
            type: 'icon_DeviceIcon',
            icon: 'fcu',
            width: 32,
            height: 32,
            fontSize: 8,
            color: '#fff'
          },
          {
            name: '空调机',
            type: 'icon_DeviceIcon',
            icon: 'ac',
            width: 32,
            height: 32,
            fontSize: 8,
            color: '#fff'
          },
          {
            name: '气体传感器',
            type: 'icon_DeviceIcon',
            icon: 'gas_sensor',
            width: 32,
            height: 32,
            fontSize: 8,
            color: '#fff'
          },
          {
            name: '水表',
            type: 'icon_DeviceIcon',
            icon: 'water_meter',
            width: 32,
            height: 32
          },
          {
            name: '电表',
            type: 'icon_DeviceIcon',
            icon: 'power_meter',
            width: 32,
            height: 32
          },
          {
            name: '水浸传感器',
            type: 'icon_DeviceIcon',
            icon: 'water_sensor',
            width: 32,
            height: 32
          },
          {
            name: '活物传感器',
            type: 'icon_DeviceIcon',
            icon: 'living_sensor',
            width: 32,
            height: 32
          },
          {
            name: '排风机',
            type: 'icon_DeviceIcon',
            icon: 'ven_fan',
            width: 32,
            height: 32
          }
        ]
      },
      {
        groupName: '原理图',
        widgets: [
          {
            name: '冷却塔',
            type: 'diagram',
            icon: 'ct',
            width: 40,
            height: 40
          },
          {
            name: '风冷热泵机组',
            type: 'diagram',
            icon: 'achpu',
            width: 40,
            height: 40
          },
          {
            name: '风机盘管物联控制器',
            type: 'diagram',
            icon: 'fcuic',
            width: 40,
            height: 40
          },
          {
            name: '水路电动三通调节阀',
            type: 'diagram',
            icon: 'wetwv',
            width: 40,
            height: 40
          },
          {
            name: '手动蝶阀',
            type: 'diagram',
            icon: 'mbv',
            width: 30,
            height: 30
          },
          {
            name: '电动动蝶阀',
            type: 'diagram',
            icon: 'ebv',
            width: 30,
            height: 30
          },
          {
            name: '水路止回阀',
            type: 'diagram',
            icon: 'wcv',
            width: 30,
            height: 30
          },
          {
            name: '水路限流止回阀',
            type: 'diagram',
            icon: 'wflcv',
            width: 30,
            height: 30
          },
          {
            name: '动态流量平衡阀',
            type: 'diagram',
            icon: 'dfbv',
            width: 30,
            height: 30
          },
          {
            name: '弹簧安全阀',
            type: 'diagram',
            icon: 'ssv',
            width: 30,
            height: 30
          },
          {
            name: '自动洗冲排污过滤器',
            type: 'diagram',
            icon: 'afsf',
            width: 50,
            height: 40
          },
          {
            name: '压差电动潘通调节阀',
            type: 'diagram',
            icon: 'dpeprv',
            width: 60,
            height: 40
          },
          {
            name: '电动比例积分阀',
            type: 'diagram',
            icon: 'epiv',
            width: 40,
            height: 30
          },
          {
            name: '压力表',
            type: 'diagram',
            icon: 'pg',
            width: 40,
            height: 30
          },
          {
            name: '软接头1',
            type: 'diagram',
            icon: 'sj',
            width: 30,
            height: 30
          },
          {
            name: '软接头2',
            type: 'diagram',
            icon: 'saj',
            width: 30,
            height: 30
          },
          {
            name: 'Y形除污器',
            type: 'diagram',
            icon: 'Ysdr',
            width: 30,
            height: 30
          },
          {
            name: '水流开关',
            type: 'diagram',
            icon: 'fs',
            width: 30,
            height: 30
          },
          {
            name: '浮球阀',
            type: 'diagram',
            icon: 'fv',
            width: 50,
            height: 30
          },
          {
            name: '自动排气阀',
            type: 'diagram',
            icon: 'aev',
            width: 30,
            height: 30
          },
          {
            name: '温度计',
            type: 'diagram',
            icon: 'tm',
            width: 30,
            height: 30
          },
          {
            name: '水表',
            type: 'diagram',
            icon: 'wm',
            width: 30,
            height: 30
          },
          {
            name: '流量计1',
            type: 'diagram',
            icon: 'fm1',
            width: 40,
            height: 40
          },
          {
            name: '流量计2',
            type: 'diagram',
            icon: 'fm2',
            width: 40,
            height: 40
          },
          {
            name: '冷冻水供水管',
            type: 'diagram',
            icon: 'cwsp',
            width: 50,
            height: 40
          },
          {
            name: '冷冻水回水管',
            type: 'diagram',
            icon: 'cwrp',
            width: 50,
            height: 40
          },
          {
            name: '泄水阀',
            type: 'diagram',
            icon: 'dv',
            width: 40,
            height: 30
          },
          {
            name: '涡轮蝶阀',
            type: 'diagram',
            icon: 'tbv',
            width: 40,
            height: 30
          },
          {
            name: '冷水机组1',
            type: 'diagram',
            icon: 'chiller1',
            width: 50,
            height: 40
          },
          {
            name: '冷水机组2',
            type: 'diagram',
            icon: 'chiller2',
            width: 50,
            height: 40
          },
          {
            name: '水泵1',
            type: 'diagram',
            icon: 'wp1',
            width: 30,
            height: 40
          },
          {
            name: '水泵2',
            type: 'diagram',
            icon: 'wp2',
            width: 60,
            height: 40
          },
          {
            name: 'U形管',
            type: 'diagram',
            icon: 'Ust',
            width: 60,
            height: 30
          },
          {
            name: '温湿度传感器',
            type: 'diagram',
            icon: 'ths',
            width: 40,
            height: 30
          },
          {
            name: '温度传感器',
            type: 'diagram',
            icon: 'ts',
            width: 40,
            height: 30
          },
          {
            name: '压力传感器',
            type: 'diagram',
            icon: 'ps',
            width: 40,
            height: 30
          }
        ]
      },
      {
        groupName: '实物图',
        widgets: [
          {
            name: '冷却塔',
            type: 'icon_AcDevice',
            icon: 'cooling_tower',
            width: 60,
            height: 50,
            fan: 'fan_1'
          },
          {
            name: '机台水泵',
            type: 'icon_AcDevice',
            icon: 'water_pump',
            width: 70,
            height: 50,
            fan: 'fan_2'
          },
          {
            name: '热泵',
            type: 'icon_AcDevice',
            icon: 'heat_pump',
            width: 40,
            height: 50,
            fan: 'fan_2'
          },
          {
            name: '热泵单机',
            type: 'icon_AcDevice',
            icon: 'heat_pump_a',
            width: 50,
            height: 50,
            fan: 'fan_2'
          },
          {
            name: '主机',
            type: 'icon_AcDevice',
            icon: 'host',
            width: 60,
            height: 50
          },
          {
            name: '主机2',
            type: 'icon_AcDevice',
            icon: 'host_2',
            width: 70,
            height: 50
          },
          {
            name: '离心机',
            type: 'icon_AcDevice',
            icon: 'centrifuge',
            width: 70,
            height: 50
          },
          {
            name: '风冷螺杆机1',
            type: 'icon_AcDevice',
            icon: 'ac_screw_1',
            width: 40,
            height: 50,
            fan: 'fan_1'
          },
          {
            name: '风冷螺杆机2',
            type: 'icon_AcDevice',
            icon: 'ac_screw_2',
            width: 60,
            height: 50,
            fan: 'fan_1'
          },
          {
            name: '风冷螺杆机3',
            type: 'icon_AcDevice',
            icon: 'ac_screw_3',
            width: 60,
            height: 50,
            fan: 'fan_1'
          },
          {
            name: '膨胀水箱',
            type: 'icon_AcDevice',
            icon: 'expand_tank',
            width: 50,
            height: 50,
            fan: 'fan_1'
          },
          {
            name: '阀门',
            type: 'icon_AcDevice',
            icon: 'valve',
            width: 40,
            height: 50,
            fan: 'fan_1'
          },
          {
            name: '分水器',
            type: 'icon_AcDevice',
            icon: 'water_sep',
            width: 80,
            height: 50
          },
          {
            name: '集水器',
            type: 'icon_AcDevice',
            icon: 'water_col',
            width: 80,
            height: 50
          },
          {
            name: '风机盘管',
            type: 'icon_AcDevice',
            icon: 'fcu',
            width: 90,
            height: 46,
            fan: 'fan_2'
          },
          {
            name: '锅炉',
            type: 'icon_AcDevice',
            icon: 'boiler',
            width: 70,
            height: 46
          },
          {
            name: '冷量计',
            type: 'icon_AcDevice',
            icon: 'cooling_meter',
            width: 25,
            height: 50
          }
        ]
      },
      {
        groupName: '建筑',
        widgets: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15].map(idx => {
          return {
            name: '建筑' + idx,
            type: 'building',
            icon: 'building_' + idx,
            height: 50,
            width: 50
          }
        })
      },
      {
        groupName: '图表',
        widgets: [
          {
            name: '参数表格',
            tableName: '',
            tableColumn: 2,
            type: 'chart_ParamTable',
            chart_type: 'table',
            // url: require('@/assets/web-styles-img/chart_default.png'),
            // width: 140,
            // height: 100,
            width: 340,
            height: 90,
            fontSize: 18,
            stroke: '#000000',
            strokeW: 1,
            opacity: 100,
            fontColor: '#ffff',
            bgColor: 'transparent',
            borderColor: '#fff',
            value: '',
            align: 'center',
            unit: ''
          },
          {
            name: '空样式表格',
            tableName: '',
            tableColumn: 4, // 表格列数
            tableRow: 2, // 表格行数
            type: 'chart_PureTable',
            chart_type: 'pure_table',
            width: 340,
            height: 90,
            fontSize: 18,
            stroke: '#000000',
            strokeW: 1,
            opacity: 100,
            cellHeight: 80, // 单元格高度
            fontColor: '#ffff',
            borderColor: '#24292C', // '#233556',
            firstColumnColor: 'rgba(38, 96, 242, 0.43)',
            firstColumnFontColor: '#62f6f9',
            headerColor: '#2762F2',
            headerFontColor: '#fff',
            contentBgColor: 'linear-gradient(180deg, rgba(71,137,250,0.2) 0%, rgba(50,105,213,0.25) 33%, rgba(32,98,253,0.2) 100%)', // '#25303e',
            contentFontColor: '#fff',
            value: '',
            align: 'center',
            unit: ''
          },
          {
            name: '柱状图',
            type: 'chart_Chart',
            chart_type: 'bar',
            url: require('@/assets/web-styles-img/chart-bar.png'),
            width: 80,
            height: 50,
            stroke: '#000000',
            strokeW: 1,
            opacity: 100,
            round: 0
          },
          {
            name: '折线图',
            type: 'chart_Chart',
            chart_type: 'line',
            url: require('@/assets/web-styles-img/chart-line.png'),
            width: 80,
            height: 50,
            stroke: '#000000',
            strokeW: 1,
            opacity: 100,
            round: 0
          },
          {
            name: '饼图',
            type: 'chart_Chart',
            chart_type: 'pie',
            url: require('@/assets/web-styles-img/chart-pie.png'),
            width: 80,
            height: 50,
            stroke: '#000000',
            strokeW: 1,
            opacity: 100,
            round: 0
          },
          {
            name: '散点图',
            type: 'chart_Chart',
            chart_type: 'scatter',
            url: require('@/assets/web-styles-img/chart-line.png'),
            width: 80,
            height: 50,
            stroke: '#000000',
            strokeW: 1,
            opacity: 100,
            round: 0
          }
        ]
      },
      {
        groupName: '设备分组',
        widgets: deviceGroups
      }
    ]
  }
  return widgets
}

export default initializeWidgets
