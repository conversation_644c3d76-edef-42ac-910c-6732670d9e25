<template>
  <div class="arrow" :style="{ width: sample ? '100%' : cover.width + 'px', height:sample ? cover.height*2 + 'px': '100%' }">
    <div class="box">
      <div
        :class="[sample || 'shape']"
        :style="{
          width: sample ? '100%' : cover.width * 100 + 'px',
          height:sample ? cover.height + 'px': cover.height/2 + 'px',
          background: `-webkit-repeating-linear-gradient(0deg, ${
            cover.colorStart1
          } 0, ${cover.colorEnd1} ${cover.width}px, ${cover.colorEnd1} ${
            cover.width
          }px, ${cover.colorStart1} ${cover.width * 2}px)`
        }"
      />
    </div>

    <div class="box another">
      <div
        :class="[sample || 'shape']"
        :style="{
          width: sample ? '100%' : cover.width * 100 + 'px',
          height:sample ? cover.height + 'px': cover.height/2 + 'px',
          background: `-webkit-repeating-linear-gradient(0deg, ${
            cover.colorStart2
          } 0, ${cover.colorEnd2} ${cover.width}px, ${cover.colorEnd2} ${
            cover.width
          }px, ${cover.colorStart2} ${cover.width * 2}px)`
        }"
      />
    </div>
  </div>
</template>

<script>
export default {
  name: 'ArrowDouble',
  props: {
    cover: {
      type: Object,
      default: () => ({
        type: 'VueComponent',
        name: 'Arrow',
        colorStart1: '#52D7F7',
        colorEnd1: '#1381bb',
        colorStart2: '#F82959',
        colorEnd2: '#F82959',
        width: 100,
        height: 20
      })
    },
    sample: {
      type: Boolean,
      default: false
    }
    // isSample: {
    //   type: Boolean,
    //   default: false
    // }
  },
  data() {
    return {}
  },
  methods: {}
}
</script>

<style lang="scss" scoped>
.arrow {
  @keyframes progressing {
    0% {
      //background-position: 0 0;
      transform: translateX(-90%);
    }
    //50%{
    //  //background-position: 500px 0;
    //  transform: translateX(-100%);
    //}
    100% {
      transform: translateX(-10%);
    }
  }

  .box {
    // clip-path: polygon(
    //   0 30%,
    //   80% 30%,
    //   80% 0,
    //   100% 50%,
    //   80% 100%,
    //   80% 70%,
    //   0 70%
    // );
    clip-path: polygon(
      1% 32%,
      79% 32%,
      79% 2%,
      99% 50%,
      79% 98%,
      79% 68%,
      1% 68%
    );//消除边线
    overflow: hidden;
  }
  .shape {
    //clip-path: polygon(0% 20%, 60% 20%, 60% 0%, 100% 50%, 60% 100%, 60% 80%, 0% 80%);
    animation: progressing 100s linear infinite;

    //消除边线？
    // border: none;
    // outline: none;
    // box-shadow: 0 0 4px 2px rgba(19,129,187,0.15);
  }
}
.another{
  transform: rotateY(180deg);

}
</style>
