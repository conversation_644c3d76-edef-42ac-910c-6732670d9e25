<template>
  <div class="arrow-settings">
    <div class="setting">
      <div class="label">箭头1颜色一</div>
      <ColorPicker color-key="colorStart1" :cover="cover" />
    </div>
    <div class="setting">
      <div class="label">箭头1颜色二</div>
      <ColorPicker color-key="colorEnd1" :cover="cover" />
    </div>
    <div class="setting">
      <div class="label">箭头2颜色一</div>
      <ColorPicker color-key="colorStart2" :cover="cover" />
    </div>
    <div class="setting">
      <div class="label">箭头2颜色二</div>
      <ColorPicker color-key="colorEnd2" :cover="cover" />
    </div>
  </div>
</template>

<script>
import ColorPicker from '@/views/svg/components/ColorPicker.vue'

export default {
  name: 'ArrowDoubleSetting',
  components: { ColorPicker },
  props: {
    cover: {
      type: Object,
      default: () => ({})
    }
  }
}
</script>

<style scoped lang="scss">

</style>
