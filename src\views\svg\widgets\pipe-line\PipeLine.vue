<template>
  <div class="img-container" :style="iconStyle">
    <div v-if="cover.color === 'custom'" ref="pipelineSvg" :class="['svg-pipeline', `${cover.icon}-${cover.id}`]" v-html="svgContent" />
    <img v-else :alt="cover.name" :src="iconSource" :height="cover.height" :width="cover.width" />
    <!-- 判断显示参数和单位 -->
    <div v-if="cover.showParams" class="param-value" :class="cover.paramsPosition" :style="{ fontSize: cover.fontSize + 'px' }">
      {{ textValue }}
    </div>
  </div>
</template>
<script>
export default {
  name: 'PipeLine',

  props: {
    cover: {
      type: Object,
      default: () => ({
        name: '管道',
        type: 'icon_PipeLine',
        icon: 'pipeline1',
        showParams: false,
        paramsPosition: null,
        width: 70,
        height: 40,
        color: 'green',
        colorStart: '#00d025',
        colorEnd: '#00d025',
        portColor: '#00d025',
        lightColor: '#fff'
      })
    },
    sample: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      // svgContent: ''
    }
  },
  computed: {
    iconSource() {
      if (this.cover.color === 'custom') {
        return require(`@/assets/svg/pipeline/custom/${this.cover.icon}.svg`)
      }
      return require(`@/assets/svg/pipeline/${this.cover.color}/${this.cover.icon}.svg`)
    },
    svgContent() {
      const mod = require(`@/assets/svg/pipeline/custom/${this.cover.icon}.svg?raw`)
      return typeof mod === 'string' ? mod : mod.default || ''
    },
    iconStyle() {
      const result = { width: this.cover.width + 'px', height: this.cover.height + 'px' }
      if (this.sample) {
        if (this.cover.width === 100) {
          result.width = '100%'
        }
      }
      return result
    },

    textValue() {
      if (this.cover.value && this.cover.showParams) {
        return this.cover.value + (this.cover.showUnit ? this.cover.unit : '')
      }
      return null
    }
  },
  watch: {
    cover: {
      handler(val) {
        if (val.color === 'custom') {
          this.$nextTick(() => {
            this.updateSvgStyle()
          })
        }
      },
      immediate: true,
      deep: true
    }
  },
  mounted() {
    // this.loadSvgContent()
  },
  methods: {
    async loadSvgContent() {
      if (this.cover.color === 'custom') {
        try {
          // const svgPath = require(`@/assets/svg/pipeline/custom/${this.cover.icon}.svg `)
          // const response = await fetch(svgPath)
          // const svgText = await response.text()
          // this.svgContent = svgPath
          this.$nextTick(() => {
            this.updateSvgStyle()
          })
        } catch (error) {
          // console.error('Failed to load SVG:', error)
          // // 降级到使用 iconSource
          // this.svgContent = this.iconSource
        }
      }
    },
    updateSvgStyle() {
      if (!this.$refs.pipelineSvg) return
      const svgElement = this.$refs.pipelineSvg.querySelector('svg')
      if (svgElement) {
        const gradient1 = svgElement.querySelector('#a')
        const gradient2 = svgElement.querySelector('#c')

        switch (this.cover.icon) {
          case 'pipeline1': {
            // 处理形状填充色
            if (gradient1) {
              const stops = gradient1.querySelectorAll('stop')
              if (stops.length >= 2) {
                stops[0].setAttribute('stop-color', this.cover.portColor || '#00d025')
                stops[1].setAttribute('stop-color', this.cover.portColor || '#00d025')
              }
            }
            if (gradient2) {
              const stops = gradient2.querySelectorAll('stop')
              if (stops.length >= 2) {
                stops[0].setAttribute('stop-color', this.cover.colorStart || '#00d025')
                stops[1].setAttribute('stop-color', this.cover.colorEnd || '#00d025')
              }
            }
            const lightElements = svgElement.querySelectorAll('.c')
            lightElements.forEach(el => {
              el.setAttribute('fill', this.cover.lightColor || '#fff')
            })
            break
          }
          case 'pipeline7': {
            const dElements = svgElement.querySelectorAll('.d')
            const eElements = svgElement.querySelectorAll('.e')
            dElements.forEach(el => {
              el.setAttribute('fill', this.cover.lightColor || '#fff')
            })
            eElements.forEach(el => {
              el.setAttribute('stroke', this.cover.lightColor || '#fff')
            })
            break
          }
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.img-container {
  position: relative;
  overflow: visible;
}
.svg-pipeline {
  width: 100%;
  height: 100%;
  display: block;
}
.svg-pipeline svg {
  width: 100%;
  height: 100%;
  display: block;
}
.param-value {
  position: absolute;
  overflow: visible;
  color: #ffffff;
  padding: 2px 8px;
  pointer-events: none;
}
.param-value.top-left {
  top: -28px;
  left: -60%;
}
.param-value.top-center {
  top: -28px;
  left: 50%;
  transform: translateX(-50%);
}
.param-value.top-right {
  top: -28px;
  right: -60%;
}
.param-value.bottom-left {
  bottom: -28px;
  left: -60%;
}
.param-value.bottom-center {
  bottom: -28px;
  left: 50%;
  transform: translateX(-50%);
}
.param-value.bottom-right {
  bottom: -28px;
  right: -60%;
}
</style>
