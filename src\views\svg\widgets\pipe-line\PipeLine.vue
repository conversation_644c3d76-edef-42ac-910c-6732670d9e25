<template>
  <div class="img-container" :style="iconStyle">
    <!-- <component :is="currentPipelineSvg" v-if="cover.color === 'custom'" ref="pipelineSvg" :class="['svg-pipeline', `${cover.icon}-${cover.id}`]" /> -->
    <div v-if="cover.color === 'custom'" ref="pipelineSvg" :class="['svg-pipeline', `${cover.icon}-${cover.id}`]" v-html="processedSvgContent" />
    <img v-else :alt="cover.name" :src="iconSource" :height="cover.height" :width="cover.width" />
    <!-- 判断显示参数和单位 -->
    <div v-if="cover.showParams" class="param-value" :class="cover.paramsPosition" :style="{ fontSize: cover.fontSize + 'px' }">
      {{ textValue }}
    </div>
  </div>
</template>
<script>
// 批量导入 pipeline1-10 的 svg
import Pipeline1Svg from '@/assets/svg/pipeline/custom/pipeline1.svg'
import Pipeline2Svg from '@/assets/svg/pipeline/custom/pipeline2.svg'
import Pipeline3Svg from '@/assets/svg/pipeline/custom/pipeline3.svg'
import Pipeline4Svg from '@/assets/svg/pipeline/custom/pipeline4.svg'
import Pipeline5Svg from '@/assets/svg/pipeline/custom/pipeline5.svg'
import Pipeline6Svg from '@/assets/svg/pipeline/custom/pipeline6.svg'
import Pipeline7Svg from '@/assets/svg/pipeline/custom/pipeline7.svg'
import Pipeline8Svg from '@/assets/svg/pipeline/custom/pipeline8.svg'
import Pipeline9Svg from '@/assets/svg/pipeline/custom/pipeline9.svg'
import Pipeline10Svg from '@/assets/svg/pipeline/custom/pipeline10.svg'

export default {
  name: 'PipeLine',
  components: {
    Pipeline1Svg,
    Pipeline2Svg,
    Pipeline3Svg,
    Pipeline4Svg,
    Pipeline5Svg,
    Pipeline6Svg,
    Pipeline7Svg,
    Pipeline8Svg,
    Pipeline9Svg,
    Pipeline10Svg
  },
  props: {
    cover: {
      type: Object,
      default: () => ({
        name: '管道',
        type: 'icon_PipeLine',
        icon: 'pipeline1',
        showParams: false,
        paramsPosition: null,
        width: 70,
        height: 40,
        color: 'green',
        colorStart: '#00d025',
        colorEnd: '#00d025',
        portColor: '#00d025',
        lightColor: '#fff'
      })
    },
    sample: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      uniqueIdSuffix: Math.random()
        .toString(36)
        .substring(2, 11)
    }
  },
  computed: {
    iconSource() {
      if (this.cover.color === 'custom') {
        return require(`@/assets/svg/pipeline/custom/${this.cover.icon}.svg`)
      }
      return require(`@/assets/svg/pipeline/${this.cover.color}/${this.cover.icon}.svg`)
    },
    svgContent() {
      try {
        const mod = require(`@/assets/svg/pipeline/custom/${this.cover.icon}.svg?raw`)
        return typeof mod === 'string' ? mod : mod.default || ''
      } catch (error) {
        console.warn('Failed to load SVG content:', error)
        return ''
      }
    },
    uniqueId() {
      return `${this.cover.icon}-${this.cover.id || this.uniqueIdSuffix}`
    },
    processedSvgContent() {
      if (!this.svgContent) return ''

      // 处理SVG内容，确保保留defs并添加响应式属性
      let processedSvg = this.svgContent

      // 确保SVG有唯一的ID前缀，避免多个实例之间的ID冲突
      const uniqueId = this.uniqueId

      // 替换SVG中的ID，使其唯一
      processedSvg = processedSvg.replace(/id="([^"]+)"/g, (_, id) => {
        return `id="${uniqueId}-${id}"`
      })

      // 更新引用这些ID的地方
      processedSvg = processedSvg.replace(/url\(#([^)]+)\)/g, (_, id) => {
        return `url(#${uniqueId}-${id})`
      })

      // 确保SVG具有响应式属性
      if (processedSvg.includes('<svg')) {
        // 移除固定的width和height属性，添加viewBox和preserveAspectRatio
        processedSvg = processedSvg.replace(/<svg([^>]*?)>/, (_, attributes) => {
          // 保留viewBox，如果没有则从width/height创建
          let viewBox = ''
          const viewBoxMatch = attributes.match(/viewBox="([^"]*)"/)
          if (viewBoxMatch) {
            viewBox = viewBoxMatch[0]
          } else {
            const widthMatch = attributes.match(/width="([^"]*)"/)
            const heightMatch = attributes.match(/height="([^"]*)"/)
            if (widthMatch && heightMatch) {
              const width = parseFloat(widthMatch[1])
              const height = parseFloat(heightMatch[1])
              viewBox = `viewBox="0 0 ${width} ${height}"`
            }
          }

          // 构建新的SVG标签
          return `<svg ${viewBox} preserveAspectRatio="xMidYMid meet" style="width: 100%; height: 100%; max-width: 100%; max-height: 100%;">`
        })
      }

      return processedSvg
    },
    iconStyle() {
      const result = {
        width: this.cover.width + 'px',
        height: this.cover.height + 'px',
        maxWidth: '100%',
        maxHeight: '100%'
      }

      if (this.sample) {
        if (this.cover.width === 100) {
          result.width = '100%'
        }
      }

      // 确保容器不会超出父容器
      result.boxSizing = 'border-box'

      return result
    },

    textValue() {
      if (this.cover.value && this.cover.showParams) {
        return this.cover.value + (this.cover.showUnit ? this.cover.unit : '')
      }
      return null
    },
    currentPipelineSvg() {
      const pipelineMap = {
        pipeline1: Pipeline1Svg,
        pipeline2: Pipeline2Svg,
        pipeline3: Pipeline3Svg,
        pipeline4: Pipeline4Svg,
        pipeline5: Pipeline5Svg,
        pipeline6: Pipeline6Svg,
        pipeline7: Pipeline7Svg,
        pipeline8: Pipeline8Svg,
        pipeline9: Pipeline9Svg,
        pipeline10: Pipeline10Svg
      }
      return pipelineMap[this.cover.icon]
    }
  },
  watch: {
    cover: {
      handler(val) {
        if (val.color === 'custom') {
          this.$nextTick(() => {
            this.updateSvgStyle()
          })
        }
      },
      immediate: true,
      deep: true
    }
  },
  mounted() {
    if (this.cover.color === 'custom') {
      this.$nextTick(() => {
        this.updateSvgStyle()
      })
    }
  },
  methods: {
    updateSvgStyle() {
      if (!this.$refs.pipelineSvg) return
      const svgElement = this.$refs.pipelineSvg.querySelector('svg')
      if (svgElement) {
        // 获取唯一ID前缀
        const uniqueId = this.uniqueId

        // 使用唯一ID查找渐变元素
        const gradient1 = svgElement.querySelector(`#${uniqueId}-a`)
        const gradient2 = svgElement.querySelector(`#${uniqueId}-c`)

        switch (this.cover.icon) {
          case 'pipeline1': {
            // 处理形状填充色
            if (gradient1) {
              const stops = gradient1.querySelectorAll('stop')
              if (stops.length >= 2) {
                stops[0].setAttribute('stop-color', this.cover.portColor || '#00d025')
                stops[1].setAttribute('stop-color', this.cover.portColor || '#00d025')
              }
            }
            if (gradient2) {
              const stops = gradient2.querySelectorAll('stop')
              if (stops.length >= 2) {
                stops[0].setAttribute('stop-color', this.cover.colorStart || '#00d025')
                stops[1].setAttribute('stop-color', this.cover.colorEnd || '#00d025')
              }
            }
            const lightElements = svgElement.querySelectorAll('.c')
            lightElements.forEach(el => {
              el.setAttribute('fill', this.cover.lightColor || '#fff')
            })
            break
          }
          case 'pipeline7': {
            const dElements = svgElement.querySelectorAll('.d')
            const eElements = svgElement.querySelectorAll('.e')
            dElements.forEach(el => {
              el.setAttribute('fill', this.cover.lightColor || '#fff')
            })
            eElements.forEach(el => {
              el.setAttribute('stroke', this.cover.lightColor || '#fff')
            })
            break
          }
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.img-container {
  position: relative;
  overflow: visible;
  display: flex;
  align-items: center;
  justify-content: center;
}
.svg-pipeline {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  max-width: 100%;
  max-height: 100%;
}
.svg-pipeline svg {
  width: 100%;
  height: 100%;
  display: block;
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}
.param-value {
  position: absolute;
  overflow: visible;
  color: #ffffff;
  padding: 2px 8px;
  pointer-events: none;
}
.param-value.top-left {
  top: -28px;
  left: -60%;
}
.param-value.top-center {
  top: -28px;
  left: 50%;
  transform: translateX(-50%);
}
.param-value.top-right {
  top: -28px;
  right: -60%;
}
.param-value.bottom-left {
  bottom: -28px;
  left: -60%;
}
.param-value.bottom-center {
  bottom: -28px;
  left: 50%;
  transform: translateX(-50%);
}
.param-value.bottom-right {
  bottom: -28px;
  right: -60%;
}
</style>
