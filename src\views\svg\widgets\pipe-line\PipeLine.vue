<template>
  <div class="img-container" :style="iconStyle">
    <!-- <component :is="currentPipelineSvg" v-if="cover.color === 'custom'" ref="pipelineSvg" :class="['svg-pipeline', `${cover.icon}-${cover.id}`]" /> -->
    <div v-if="cover.color === 'custom'" :class="['svg-pipeline', `${cover.icon}-${cover.id}`]" v-html="svgContent" />
    <img v-else :alt="cover.name" :src="iconSource" :height="cover.height" :width="cover.width" />
    <!-- 判断显示参数和单位 -->
    <div v-if="cover.showParams" class="param-value" :class="cover.paramsPosition" :style="{ fontSize: cover.fontSize + 'px' }">
      {{ textValue }}
    </div>
  </div>
</template>
<script>
// 批量导入 pipeline1-10 的 svg
import Pipeline1Svg from '@/assets/svg/pipeline/custom/pipeline1.svg'
import Pipeline2Svg from '@/assets/svg/pipeline/custom/pipeline2.svg'
import Pipeline3Svg from '@/assets/svg/pipeline/custom/pipeline3.svg'
import Pipeline4Svg from '@/assets/svg/pipeline/custom/pipeline4.svg'
import Pipeline5Svg from '@/assets/svg/pipeline/custom/pipeline5.svg'
import Pipeline6Svg from '@/assets/svg/pipeline/custom/pipeline6.svg'
import Pipeline7Svg from '@/assets/svg/pipeline/custom/pipeline7.svg'
import Pipeline8Svg from '@/assets/svg/pipeline/custom/pipeline8.svg'
import Pipeline9Svg from '@/assets/svg/pipeline/custom/pipeline9.svg'
import Pipeline10Svg from '@/assets/svg/pipeline/custom/pipeline10.svg'

export default {
  name: 'PipeLine',
  components: {
    Pipeline1Svg,
    Pipeline2Svg,
    Pipeline3Svg,
    Pipeline4Svg,
    Pipeline5Svg,
    Pipeline6Svg,
    Pipeline7Svg,
    Pipeline8Svg,
    Pipeline9Svg,
    Pipeline10Svg
  },
  props: {
    cover: {
      type: Object,
      default: () => ({
        name: '管道',
        type: 'icon_PipeLine',
        icon: 'pipeline1',
        showParams: false,
        paramsPosition: null,
        width: 70,
        height: 40,
        color: 'green',
        colorStart: '#00d025',
        colorEnd: '#00d025',
        portColor: '#00d025',
        lightColor: '#fff'
      })
    },
    sample: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      // iconSource: require(`@/assets/svg/pipeline/${this.cover.color}/${this.cover.icon}.svg`)
      // svgContent: ''
    }
  },
  computed: {
    iconSource() {
      if (this.cover.color === 'custom') {
        return require(`@/assets/svg/pipeline/custom/${this.cover.icon}.svg`)
      }
      return require(`@/assets/svg/pipeline/${this.cover.color}/${this.cover.icon}.svg`)
    },
    svgContent() {
      return this.iconSource.text()
    },
    iconStyle() {
      const result = { width: this.cover.width + 'px', height: this.cover.height + 'px' }
      if (this.sample) {
        if (this.cover.width === 100) {
          result.width = '100%'
        }
      }
      return result
    },

    textValue() {
      if (this.cover.value && this.cover.showParams) {
        return this.cover.value + (this.cover.showUnit ? this.cover.unit : '')
      }
      return null
    },
    currentPipelineSvg() {
      const pipelineMap = {
        pipeline1: Pipeline1Svg,
        pipeline2: Pipeline2Svg,
        pipeline3: Pipeline3Svg,
        pipeline4: Pipeline4Svg,
        pipeline5: Pipeline5Svg,
        pipeline6: Pipeline6Svg,
        pipeline7: Pipeline7Svg,
        pipeline8: Pipeline8Svg,
        pipeline9: Pipeline9Svg,
        pipeline10: Pipeline10Svg
      }
      return pipelineMap[this.cover.icon]
    }
  },
  watch: {
    cover: {
      handler(val) {
        if (val.color === 'custom') {
          this.$nextTick(() => {
            this.updateSvgStyle()
          })
        }
      },
      immediate: true,
      deep: true
    }
  },
  mounted() {},
  methods: {
    updateSvgStyle() {
      if (!this.$refs.pipelineSvg) return
      if (this.$refs.pipelineSvg) {
        const svgTag = document.getElementsByClassName(`${this.cover.icon}-${this.cover.id}`)[0]
        const gradient1 = svgTag.getElementById('a')
        const gradient2 = svgTag.getElementById('c')
        if (svgTag) {
          switch (this.cover.icon) {
            case 'pipeline1':
              // 处理形状填充色
              if (gradient1) {
                const stops = gradient1.getElementsByTagName('stop')

                stops[0].setAttribute('stop-color', this.cover.portColor || '#00d025')
                stops[1].setAttribute('stop-color', this.cover.portColor || '#00d025')
              }
              if (gradient2) {
                const stops = gradient2.getElementsByTagName('stop')

                stops[0].setAttribute('stop-color', this.cover.colorStart || '#00d025')
                stops[1].setAttribute('stop-color', this.cover.colorEnd || '#00d025')
              }
              svgTag.querySelector('.c').setAttribute('fill', this.cover.lightColor || '#fff')
              break
            case 'pipeline7':
              svgTag.querySelector('.d').setAttribute('fill', this.cover.lightColor || '#fff')
              svgTag.querySelector('.e').setAttribute('stroke', this.cover.lightColor || '#fff')
              break
          }
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.img-container {
  position: relative;
  overflow: visible;
}
.svg-pipeline {
  width: 100%;
  height: 100%;
  display: block;
}
.svg-pipeline svg {
  width: 100%;
  height: 100%;
  display: block;
}
.param-value {
  position: absolute;
  overflow: visible;
  color: #ffffff;
  padding: 2px 8px;
  pointer-events: none;
}
.param-value.top-left {
  top: -28px;
  left: -60%;
}
.param-value.top-center {
  top: -28px;
  left: 50%;
  transform: translateX(-50%);
}
.param-value.top-right {
  top: -28px;
  right: -60%;
}
.param-value.bottom-left {
  bottom: -28px;
  left: -60%;
}
.param-value.bottom-center {
  bottom: -28px;
  left: 50%;
  transform: translateX(-50%);
}
.param-value.bottom-right {
  bottom: -28px;
  right: -60%;
}
</style>
