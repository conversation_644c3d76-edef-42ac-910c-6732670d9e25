<template>
  <div class="svg-icon-settings">
    <div class="setting">
      <div class="label">默认样式</div>
      <el-select v-model="cover.color" size="mini" placeholder="选择样式">
        <el-option v-for="(item, index) in colorOptions" :key="index" :label="item.label" :value="item.value" />
      </el-select>
    </div>
    <template v-if="cover.color === 'custom'">
      <div class="setting">
        <div class="label">管道颜色1</div>
        <ColorPicker color-key="colorStart" :cover="cover" />
      </div>
      <div class="setting">
        <div class="label">管道颜色2</div>
        <ColorPicker color-key="colorEnd" :cover="cover" />
      </div>
      <div class="setting">
        <div class="label">两端颜色</div>
        <ColorPicker color-key="portColor" :cover="cover" />
      </div>
      <div class="setting">
        <div class="label">反光颜色</div>
        <ColorPicker color-key="lightColor" :cover="cover" />
      </div>
    </template>
    <!-- <div class="setting">
      <div class="label">显示参数</div>
      <div class="switch-value">
        <el-switch v-model="cover.showParams" />
      </div>
    </div> -->
    <!-- <div v-if="cover.showParams" class="setting">
      <div class="label">参数位置</div>
      <el-select v-model="cover.paramsPosition" size="mini" clearable placeholder="选择参数位置">
        <el-option v-for="(item, index) in paramsPositionOptions" :key="index" :label="item.label" :value="item.value" />
      </el-select>
    </div>
    <div v-if="cover.showParams" class="setting">
      <div class="label">显示单位</div>
      <div class="switch-value">
        <el-switch v-model="cover.showUnit" />
      </div>
    </div>
    <div v-if="cover.showParams" class="setting">
      <div class="label">字体大小</div>
      <el-input-number v-model="cover.fontSize" />
    </div> -->
  </div>
</template>

<script>
import ColorPicker from '@/views/svg/components/ColorPicker.vue'
export default {
  name: 'PipeLineSettings',
  components: { ColorPicker },
  props: {
    cover: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      // paramsPositionOptions: [
      //   { label: '左上', value: 'top-left' },
      //   { label: '中上', value: 'top-center' },
      //   { label: '右上', value: 'top-right' },
      //   { label: '左下', value: 'bottom-left' },
      //   { label: '中下', value: 'bottom-center' },
      //   { label: '右下', value: 'bottom-right' }
      // ],
      colorOptions: [
        { label: '绿色', value: 'green' },
        { label: '红色', value: 'red' },
        { label: '蓝色', value: 'blue' },
        { label: '黄色', value: 'yellow' },
        { label: '灰色', value: 'grey' },
        { label: '灰-黄色', value: 'grey-yellow' },
        { label: '自定义', value: 'custom' }
      ]
    }
  }
}
</script>

<style lang="scss" scoped>
.switch-value {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
}
</style>
