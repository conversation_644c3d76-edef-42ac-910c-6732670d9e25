<template>
  <div
    class="pure-table"
    :style="{ width:!sample?cover.width+'px': '12rem',
              height: !sample?cover.height+'px': '60px' }"
  >
    <template>
      <div
        class="table-content"
        :style="{
          width: !sample ? cover.width + 'px' : '100%',
          height: !sample ? cover.height + 'px' : '100%',
          background: cover.bgColor,
          fontSize: !sample ? cover.fontSize + 'px' : '0.8rem',
          color: cover.fontColor
        }"
      >

        <template v-if="sample">
          <div class="header-row">
            <div
              v-for="(item, index) in demoHRow"
              :key="index"
              class="row"
              :style="{
                justifyContent: 'center',
                height: '1.75rem',
                width: '100%',
                border : `1px solid #fff`
              }"
            >
              <div
                class="label"
                :style="{
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center'
                }"
              >
                <span> {{ item }}</span>
              </div>
            </div>
          </div>
          <div
            v-for="(item, index) in demoCRow"
            :key="index"
            :style="{
              justifyContent: 'center',
              border: `1px solid #fff`
            }"
            class="content-row"
          >
            <el-col :span="6" :style="{ justifyContent: 'center', alignItems: 'center', borderRight: `1px solid #fff` }">xx</el-col>
            <el-col :span="6" :style="{ justifyContent: 'center', alignItems: 'center', borderRight: `1px solid #fff` }">xx</el-col>
            <el-col :span="6" :style="{ justifyContent: 'center', alignItems: 'center', borderRight: `1px solid #fff` }">xx</el-col>
            <el-col :span="6" :style="{ justifyContent: 'center', alignItems: 'center', borderRight: `1px solid #fff` }">xx</el-col>
          </div>
        </template>

        <template v-else>
          <div
            class="header-row"
            :style="{
              justifyContent: cover.align,
              height: `${cover.cellHeight || 80}px`,
              border: `1px solid ${cover.borderColor}`
            }"
          >
            <div
              v-for="(item, colIndex) in tableColumns"
              :key="colIndex"
              class="item-row"
              :style="headerRowStyle()"
            >

            <!-- <span>表头{{ colIndex + 1 }}</span> -->
            </div>
          </div>
          <div class="table-block">
            <!-- <el-scrollbar style="width: 100%; height: 100%" @wheel.native.stop> -->
            <div style="width: 100%; height: 100%">
              <div
                v-for="(row, rowIndex) in tableRow"
                :key="rowIndex"
                :style="{
                  justifyContent: cover.align,
                  border: `1px solid ${cover.borderColor}`,
                  height: `${cover.cellHeight || 80}px`
                }"
                class="content-row"
              >
                <div
                  v-for="(col, colIndex) in tableColumns"
                  :key="colIndex"
                  class="item-row"
                  :style="contentSpanStyle(rowIndex, colIndex)"
                >

                <!-- <span>单元格 {{ rowIndex + 1 }}-{{ colIndex + 1 }}</span> -->
                </div>
              </div>
            </div>
            <!-- </el-scrollbar> -->
          </div>
        </template>
      </div>
    </template>

  </div>
</template>

<script>
import { createUniqueString } from '@/utils'

export default {
  name: 'PureTable',
  props: {
    cover: {
      type: Object,
      default: () => ({
        id: createUniqueString(),
        name: '空样式表格',
        tableName: '',
        tableColumn: 3, // 表格列数
        tableRow: 2, // 表格行数
        type: 'chart_PureTable',
        chart_type: 'pure_table',
        width: 340,
        height: 90,
        fontSize: 18,
        stroke: '#000000',
        strokeW: 1,
        opacity: 100,
        fontColor: '#ffff',
        value: '',
        align: 'center',
        unit: ''
      })
    },
    idx: {
      type: Number,
      default: 0
    },
    sample: {
      type: Boolean,
      default: false
    },
    editing: {
      type: Boolean,
      default: false
    }

  },
  data() {
    return {
      left0Prefix: [],
      zoneRightIcon: [],
      demoHRow: ['参数1', '参数2', '参数3', '参数4'],
      demoCRow: [{}, {}],
      groupHRow: ['昵称', '运行参数', '设定参数', '开关状态'],
      groupCRow: []
    }
  },
  computed: {
    xPosition() {
      let width = this.cover.width
      if (this.sample && this.textBoundingWidth) {
        width = this.textBoundingWidth
      }
      if (this.cover.align === 'middle') {
        return width / 2
      } else if (this.cover.align === 'end') {
        return width
      }
      return 0
    },
    condition() {
      if (this.cover.condition) {
        return this.evalCondition(this.cover.condition)
      }
      return true
    },

    noData() {
      return !this.cover.values || this.cover.values.length == 0
    },
    tableRow() {
      return Array(this.cover.tableRow).fill('')
    },
    tableColumns() {
      return Array(this.cover.tableColumn).fill('')
    }
  },
  watch: {
    cover: {
      handler(val) {
        this.$nextTick(function() {
          if (val.values && val.values.length > 0) {
            this.groupCRow = val.values.map(item => {
              return item
            })
          } else {
            this.groupCRow = []
          }
        })
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    headerRowStyle() {
      return {
        minWidth: '100px',
        height: '100%',
        alignItems: 'center',
        display: 'flex',
        flex: 1,
        justifyContent: this.cover.align,
        color: this.cover.headerFontColor,
        background: this.cover.headerColor,
        borderRight: `1px solid ${this.cover.borderColor}`
      }
    },
    contentSpanStyle(rowIndex, colIndex) {
      return {
        minWidth: '100px',
        height: '100%',
        alignItems: 'center',
        display: 'flex',
        flex: 1,
        justifyContent: this.cover.align,
        borderRight: `1px solid ${this.cover.borderColor}`,
        background: colIndex == 0 ? this.cover.firstColumnColor : this.cover.contentBgColor,
        color: colIndex == 0 ? this.cover.firstColumnFontColor : this.cover.contentFontColor
      }
    },
    colVal(name, val, unit) {
      let res = '--'
      const c_unit = unit || ''
      if (name) {
        res = `${name}：${val}${c_unit}`
      } else {
        res = `${val}${c_unit}`
      }
      return res
    },
    getDeviceStatus(val) {
      // const status = getDeviceStatus(val.status, val.in_alarm, val.sw_on)
      const status = val.sw_on ? '开' : '关'
      return status
    }
  }
}
</script>

<style lang="scss" scoped>
.pure-table {
  align-items: center;
  display: flex;
  width: 100%;

  .table-content {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    flex-direction: column;
  }
  .header-row {
    width: 100%;
    display: flex;
    align-items: center;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    height: 60px;

    .el-col {
      align-items: center;
      display: flex;
      &:last-child {
        border-right: none;
      }
    }
  }
  .table-block {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    /deep/ .el-scrollbar__thumb {
      background: #fff !important;
      opacity: 0.8 !important;
    }
  }

  .content-row {
    width: 100%;
    display: flex;
    align-items: center;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    height: 60px;
    //滚动条的滑块

    .el-col {
      align-items: center;
      display: flex;
      &:last-child {
        border-right: none;
      }
    }
    .col-value {
      span {
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
        flex-wrap: nowrap;
        display: inline-block;
        max-width: 100%;
        &:last-child {
          border-right: none;
        }
      }
    }
  }
}
</style>
