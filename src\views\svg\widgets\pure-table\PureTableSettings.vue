<template>
  <div class="param-table-settings">
    <!-- <div class="setting">
      <div class="label">表格名称</div>
      <el-input v-model="cover.tableName" />
    </div> -->
    <div class="setting">
      <div class="label">表格列数</div>
      <el-input-number v-model="cover.tableColumn" />
    </div>
    <div class="setting">
      <div class="label">表格行数</div>
      <el-input-number v-model="cover.tableRow" />
    </div>
    <div class="setting">
      <div class="label">单元格高度</div>
      <el-input-number v-model="cover.cellHeight" />
    </div>
    <div class="setting">
      <div class="label">边框颜色</div>
      <ColorPicker :cover="cover" color-key="borderColor" />
    </div>
    <!-- <div class="setting">
      <div class="label">字体大小</div>
      <el-input-number v-model="cover.fontSize" />
    </div> -->

    <!-- <div class="setting">
      <div class="label">首列背景色</div>
      <ColorPicker :cover="cover" color-key="firstColumnColor" />
    </div>
    <div class="setting">
      <div class="label">首列字体色</div>
      <ColorPicker :cover="cover" color-key="firstColumnFontColor" />
    </div>
    <div class="setting">
      <div class="label">表头背景色</div>
      <ColorPicker :cover="cover" color-key="headerColor" />
    </div>
    <div class="setting">
      <div class="label">表头字体色</div>
      <ColorPicker :cover="cover" color-key="headerFontColor" />
    </div>
    <div class="setting">
      <div class="label">内容背景色</div>
      <ColorPicker :cover="cover" color-key="contentBgColor" />
    </div>
    <div class="setting">
      <div class="label">内容字体色</div>
      <ColorPicker :cover="cover" color-key="contentFontColor" />
    </div> -->
    <!-- <div class="setting">
      <div class="label">对齐</div>
      <el-radio-group v-model="cover.align" size="mini">
        <el-radio-button label="flex-start">左</el-radio-button>
        <el-radio-button label="center">中</el-radio-button>
        <el-radio-button label="flex-end">右</el-radio-button>
      </el-radio-group>
    </div> -->
  </div>
</template>

<script>
import ColorPicker from '@/views/svg/components/ColorPicker.vue'

export default {
  name: 'ParamTableSettings',
  components: { ColorPicker },
  props: {
    cover: {
      type: Object,
      default: () => ({})
    }
  }
}
</script>

<style scoped lang="scss">

</style>
